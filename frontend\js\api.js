// API Client for Solar Monitor Dashboard

class SolarAPI {
    constructor() {
        this.baseURL = 'http://localhost:8000/api';
        this.accessCode = null;
        this.isAuthenticated = false;
    }

    // Set the access code for authentication
    setAccessCode(code) {
        this.accessCode = code;
        this.isAuthenticated = true;
        // Store in localStorage for persistence
        localStorage.setItem('solar_access_code', code);
    }

    // Get access code from localStorage
    getStoredAccessCode() {
        return localStorage.getItem('solar_access_code');
    }

    // Clear stored credentials
    clearCredentials() {
        this.accessCode = null;
        this.isAuthenticated = false;
        localStorage.removeItem('solar_access_code');
    }

    // Make authenticated API request
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-Access-Code': this.accessCode
            }
        };

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, finalOptions);
            
            if (response.status === 401) {
                this.clearCredentials();
                throw new Error('Authentication failed. Please log in again.');
            }

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                let errorMessage = errorData.detail || `HTTP ${response.status}: ${response.statusText}`;
                
                // Handle specific error cases
                if (response.status === 503 && errorData.detail?.includes('Deye API credentials')) {
                    errorMessage = 'Configuration Required: Please update your .env file with real Deye API credentials from your Deye Cloud developer account.';
                }
                
                throw new Error(errorMessage);
            }

            return await response.json();
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    // Authentication
    async login(accessCode) {
        try {
            const response = await fetch(`${this.baseURL}/auth/token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ code: accessCode })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || 'Login failed');
            }

            const data = await response.json();
            this.setAccessCode(accessCode);
            return data;
        } catch (error) {
            console.error('Login failed:', error);
            throw error;
        }
    }

    // Get all solar stations
    async getStations() {
        return await this.makeRequest('/solar/stations');
    }

    // Get real-time data for a station
    async getStationLatest(stationId) {
        return await this.makeRequest(`/solar/stations/${stationId}/latest`);
    }

    // Get historical data for a station
    async getStationHistory(stationId, days = 7) {
        return await this.makeRequest(`/solar/stations/${stationId}/history?days=${days}`);
    }

    // ===== ML ANALYTICS ENDPOINTS =====

    // Get AI insights for a station
    async getStationInsights(stationId, daysBack = 7) {
        return await this.makeRequest(`/ml/analytics/insights/${stationId}?days_back=${daysBack}`);
    }

    // Get production forecast
    async getProductionForecast(stationId, hours = 24) {
        return await this.makeRequest(`/ml/analytics/forecast/${stationId}?hours=${hours}`);
    }

    // Get anomaly detection results
    async getAnomalyDetection(stationId, hoursBack = 48) {
        return await this.makeRequest(`/ml/analytics/anomalies/${stationId}?hours_back=${hoursBack}`);
    }

    // Get maintenance recommendations
    async getMaintenanceRecommendations(stationId) {
        return await this.makeRequest(`/ml/analytics/maintenance/${stationId}`);
    }

    // Get consumption insights
    async getConsumptionInsights(stationId) {
        return await this.makeRequest(`/ml/analytics/consumption/${stationId}`);
    }

    // Get performance analysis
    async getPerformanceAnalysis(stationId, daysBack = 7) {
        return await this.makeRequest(`/ml/analytics/performance/${stationId}?days_back=${daysBack}`);
    }

    // Train ML models (background task)
    async trainModels(stationId, daysBack = 30) {
        return await this.makeRequest(`/ml/analytics/train/${stationId}?days_back=${daysBack}`, {
            method: 'POST'
        });
    }

    // Check if the API is accessible
    async healthCheck() {
        try {
            const response = await fetch(`${this.baseURL.replace('/api', '/')}`);
            return response.ok;
        } catch (error) {
            console.error('Health check failed:', error);
            return false;
        }
    }
}

// Create global API instance
window.solarAPI = new SolarAPI();
