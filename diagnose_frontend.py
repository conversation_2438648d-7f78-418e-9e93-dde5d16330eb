#!/usr/bin/env python3
"""
Frontend Diagnostic Script for Solar Display Application
Helps diagnose common frontend issues
"""

import os
import socket
import subprocess
import sys
from pathlib import Path
import requests
import time

def check_port_available(port, host='127.0.0.1'):
    """Check if a port is available"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex((host, port))
            return result != 0
    except Exception:
        return False

def check_frontend_files():
    """Check if all required frontend files exist"""
    print("🔍 Checking frontend files...")
    
    frontend_dir = Path(__file__).parent / "frontend"
    required_files = [
        "index.html",
        "css/styles.css",
        "js/main.js",
        "js/api.js"
    ]
    
    if not frontend_dir.exists():
        print(f"❌ Frontend directory not found: {frontend_dir}")
        return False
    
    all_files_exist = True
    for file_path in required_files:
        full_path = frontend_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            all_files_exist = False
    
    return all_files_exist

def check_ports():
    """Check port availability"""
    print("\n🔍 Checking port availability...")
    
    ports_to_check = [3000, 8000]
    for port in ports_to_check:
        if check_port_available(port):
            print(f"✅ Port {port} is available")
        else:
            print(f"⚠️  Port {port} is in use")
            # Try to identify what's using the port
            try:
                result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
                lines = result.stdout.split('\n')
                for line in lines:
                    if f':{port}' in line and 'LISTENING' in line:
                        print(f"   Process using port {port}: {line.strip()}")
                        break
            except:
                pass

def test_backend_connection():
    """Test if backend is responding"""
    print("\n🔍 Testing backend connection...")
    
    try:
        response = requests.get('http://localhost:8000/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ Backend is responding")
            return True
        else:
            print(f"⚠️  Backend responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend (connection refused)")
        return False
    except requests.exceptions.Timeout:
        print("❌ Backend connection timeout")
        return False
    except Exception as e:
        print(f"❌ Backend connection error: {e}")
        return False

def test_frontend_server():
    """Test if frontend server can be started"""
    print("\n🔍 Testing frontend server startup...")
    
    frontend_dir = Path(__file__).parent / "frontend"
    
    try:
        # Try to start the server
        process = subprocess.Popen([
            sys.executable, "-m", "http.server", "3001", 
            "--bind", "127.0.0.1"
        ], cwd=frontend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Give it time to start
        time.sleep(3)
        
        # Check if it's running
        if process.poll() is None:
            print("✅ Frontend server can start successfully")
            
            # Test if we can access it
            try:
                response = requests.get('http://localhost:3001', timeout=5)
                if response.status_code == 200:
                    print("✅ Frontend server is accessible")
                    success = True
                else:
                    print(f"⚠️  Frontend server responded with status {response.status_code}")
                    success = False
            except Exception as e:
                print(f"❌ Cannot access frontend server: {e}")
                success = False
            
            # Clean up
            process.terminate()
            process.wait()
            return success
        else:
            stdout, stderr = process.communicate()
            print("❌ Frontend server failed to start")
            if stderr:
                print(f"Error: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to test frontend server: {e}")
        return False

def check_python_modules():
    """Check if required Python modules are available"""
    print("\n🔍 Checking Python modules...")
    
    required_modules = ['http.server', 'socketserver']
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - NOT AVAILABLE")

def main():
    """Run all diagnostic checks"""
    print("============================================================")
    print("Solar Display Frontend Diagnostic Tool")
    print("============================================================")
    
    # Run all checks
    files_ok = check_frontend_files()
    check_ports()
    backend_ok = test_backend_connection()
    check_python_modules()
    frontend_ok = test_frontend_server()
    
    print("\n============================================================")
    print("📋 DIAGNOSTIC SUMMARY")
    print("============================================================")
    
    if files_ok:
        print("✅ Frontend files: OK")
    else:
        print("❌ Frontend files: MISSING FILES")
    
    if backend_ok:
        print("✅ Backend connection: OK")
    else:
        print("❌ Backend connection: FAILED")
    
    if frontend_ok:
        print("✅ Frontend server: OK")
    else:
        print("❌ Frontend server: FAILED")
    
    print("\n🔧 RECOMMENDATIONS:")
    
    if not files_ok:
        print("- Check that all frontend files are present in the frontend/ directory")
    
    if not backend_ok:
        print("- Start the backend server first: python -m uvicorn src.main:app --host 0.0.0.0 --port 8000")
    
    if not frontend_ok:
        print("- Try running the frontend manually: cd frontend && python -m http.server 3000")
        print("- Check for port conflicts or firewall issues")
    
    if files_ok and backend_ok and frontend_ok:
        print("✅ All systems appear to be working correctly!")
        print("- Try running: python start_app_phase3.py")
    
    print("\n============================================================")

if __name__ == "__main__":
    main()
