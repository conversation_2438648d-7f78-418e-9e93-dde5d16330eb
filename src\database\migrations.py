"""
Database migration system for Solar Display application

Creates and manages SQLite database schema for solar energy analytics.
All tables are optimized for time-series data storage and querying.

Schema designed for:
- Deye inverter data collection (15-minute intervals)
- Open-Meteo weather data caching
- ML model training and inference
- ML feature engineering support
"""

import aiosqlite
import logging
from pathlib import Path
from typing import List

from src.database.connection import db, DB_PATH

logger = logging.getLogger(__name__)

# SQL Schema definitions based on PRD.md specifications
SCHEMA_VERSION = "1.0.0"

CREATE_SOLAR_PRODUCTION_TABLE = """
CREATE TABLE IF NOT EXISTS solar_production (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    station_id TEXT NOT NULL,
    total_power REAL NOT NULL,           -- Total system power (kW)
    daily_energy REAL NOT NULL,          -- Daily energy production (kWh)
    inverter_temp REAL,                  -- Inverter temperature (°C)
    dc_voltage REAL,                     -- DC input voltage (V)
    ac_voltage REAL,                     -- AC output voltage (V)
    efficiency REAL,                     -- System efficiency (%)
    grid_frequency REAL,                 -- Grid frequency (Hz)
    battery_soc REAL,                    -- Battery state of charge (%)
    battery_power REAL,                  -- Battery charge/discharge (kW)
    grid_power REAL,                     -- Grid import/export (kW)
    consumption_power REAL,              -- Current consumption (kW)
    irradiate_intensity REAL,            -- Solar irradiation (W/m²)
    error_code TEXT,                     -- Any error codes
    raw_data TEXT,                       -- Complete JSON from Deye API
    UNIQUE(timestamp, station_id)        -- Prevent duplicate entries
);
"""

CREATE_WEATHER_DATA_TABLE = """
CREATE TABLE IF NOT EXISTS weather_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    temperature REAL NOT NULL,           -- Air temperature (°C)
    humidity REAL NOT NULL,              -- Relative humidity (%)
    solar_radiation REAL NOT NULL,       -- Solar irradiance (W/m²)
    cloud_cover REAL NOT NULL,          -- Cloud coverage (%)
    wind_speed REAL NOT NULL,           -- Wind speed (m/s)
    pressure REAL,                      -- Atmospheric pressure (hPa)
    weather_code INTEGER,               -- WMO weather code
    source TEXT DEFAULT 'open-meteo',   -- Weather data source
    UNIQUE(timestamp)                   -- One weather reading per timestamp
);
"""

CREATE_ML_FEATURES_TABLE = """
CREATE TABLE IF NOT EXISTS ml_features (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME NOT NULL,
    production_kw REAL NOT NULL,
    irradiance_wm2 REAL NOT NULL,
    temperature_c REAL NOT NULL,
    cloud_cover_pct REAL NOT NULL,
    efficiency_pct REAL NOT NULL,
    season TEXT NOT NULL,               -- Summer/Winter/Autumn/Spring
    time_of_day TEXT NOT NULL,         -- Morning/Midday/Afternoon/Evening/Night
    weather_category TEXT NOT NULL,    -- Clear/Partly_Cloudy/Cloudy/Overcast/Rainy
    hour_sin REAL NOT NULL,            -- Cyclical hour encoding
    hour_cos REAL NOT NULL,
    day_of_year_sin REAL NOT NULL,     -- Cyclical day encoding
    day_of_year_cos REAL NOT NULL,
    UNIQUE(timestamp)
);
"""

# Indexes for optimized time-series queries
CREATE_INDEXES = [
    "CREATE INDEX IF NOT EXISTS idx_solar_timestamp ON solar_production(timestamp);",
    "CREATE INDEX IF NOT EXISTS idx_solar_station_time ON solar_production(station_id, timestamp);",
    "CREATE INDEX IF NOT EXISTS idx_weather_timestamp ON weather_data(timestamp);",
    "CREATE INDEX IF NOT EXISTS idx_ml_timestamp ON ml_features(timestamp);",
]

# Views for common queries
CREATE_VIEWS = [
    """
    CREATE VIEW IF NOT EXISTS daily_production_summary AS
    SELECT 
        DATE(timestamp) as date,
        station_id,
        AVG(total_power) as avg_power_kw,
        MAX(daily_energy) as total_energy_kwh,
        AVG(efficiency) as avg_efficiency_pct,
        AVG(irradiate_intensity) as avg_irradiance_wm2,
        COUNT(*) as readings_count
    FROM solar_production 
    WHERE timestamp >= date('now', '-30 days')
    GROUP BY DATE(timestamp), station_id
    ORDER BY date DESC;
    """,
    
    """
    CREATE VIEW IF NOT EXISTS weather_solar_correlation AS
    SELECT 
        w.timestamp,
        w.temperature,
        w.solar_radiation as weather_irradiance,
        w.cloud_cover,
        s.total_power,
        s.efficiency,
        s.irradiate_intensity as measured_irradiance
    FROM weather_data w
    LEFT JOIN solar_production s ON 
        datetime(w.timestamp) = datetime(s.timestamp)
    WHERE w.timestamp >= datetime('now', '-7 days')
    ORDER BY w.timestamp DESC;
    """
]

async def create_database_schema():
    """
    Create complete database schema for Solar Display application
    
    Creates all tables, indexes, and views required for:
    - Solar production data storage
    - Weather data caching  
    - ML feature engineering
    - ML feature engineering support
    """
    logger.info("Creating Solar Display database schema...")
    
    try:
        async with db.get_connection() as conn:
            # Create main tables
            await conn.execute(CREATE_SOLAR_PRODUCTION_TABLE)
            await conn.execute(CREATE_WEATHER_DATA_TABLE)
            await conn.execute(CREATE_ML_FEATURES_TABLE)
            
            # Create indexes for performance
            for index_sql in CREATE_INDEXES:
                await conn.execute(index_sql)
            
            # Create views for common queries
            for view_sql in CREATE_VIEWS:
                await conn.execute(view_sql)
            
            # Create schema version tracking
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS schema_version (
                    version TEXT PRIMARY KEY,
                    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Record schema version
            await conn.execute(
                "INSERT OR REPLACE INTO schema_version (version) VALUES (?)",
                (SCHEMA_VERSION,)
            )
            
            await conn.commit()
            logger.info(f"Database schema created successfully (version {SCHEMA_VERSION})")
            
    except Exception as e:
        logger.error(f"Failed to create database schema: {e}")
        raise

async def check_database_exists() -> bool:
    """
    Check if database file exists and has proper schema
    
    Returns:
        True if database exists with current schema, False otherwise
    """
    if not DB_PATH.exists():
        return False
    
    try:
        async with db.get_connection() as conn:
            # Check if schema_version table exists
            cursor = await conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='schema_version';
            """)
            table_exists = await cursor.fetchone()
            
            if not table_exists:
                return False
                
            # Check schema version
            cursor = await conn.execute(
                "SELECT version FROM schema_version ORDER BY applied_at DESC LIMIT 1"
            )
            version_row = await cursor.fetchone()
            
            if not version_row or version_row[0] != SCHEMA_VERSION:
                logger.warning(f"Database schema version mismatch. Expected: {SCHEMA_VERSION}, Found: {version_row[0] if version_row else 'None'}")
                return False
                
            return True
            
    except Exception as e:
        logger.error(f"Error checking database: {e}")
        return False

async def initialize_database():
    """
    Initialize database for Solar Display application
    
    Creates schema if needed, ensures proper structure exists
    for solar energy data collection and ML training.
    """
    logger.info("Initializing Solar Display database...")
    
    db_exists = await check_database_exists()
    
    if not db_exists:
        logger.info("Database not found or schema outdated, creating new schema...")
        await create_database_schema()
    else:
        logger.info("Database schema is up to date")
    
    # Verify database is working
    try:
        async with db.get_connection() as conn:
            # Test query
            cursor = await conn.execute("SELECT COUNT(*) FROM solar_production")
            count = await cursor.fetchone()
            logger.info(f"Database initialized successfully. Solar production records: {count[0]}")
            
    except Exception as e:
        logger.error(f"Database verification failed: {e}")
        raise

async def get_database_stats() -> dict:
    """
    Get database statistics for monitoring and debugging
    
    Returns:
        Dictionary with table row counts and database size
    """
    try:
        async with db.get_connection() as conn:
            stats = {}
            
            # Get row counts for each table
            tables = ['solar_production', 'weather_data', 'ml_features', 'anomalies']
            for table in tables:
                cursor = await conn.execute(f"SELECT COUNT(*) FROM {table}")
                count = await cursor.fetchone()
                stats[f"{table}_count"] = count[0]
            
            # Get database file size
            stats['database_size_mb'] = round(DB_PATH.stat().st_size / (1024 * 1024), 2)
            
            # Get date range of data
            cursor = await conn.execute("""
                SELECT 
                    MIN(timestamp) as earliest_record,
                    MAX(timestamp) as latest_record
                FROM solar_production
            """)
            date_range = await cursor.fetchone()
            if date_range[0]:
                stats['data_range'] = {
                    'earliest': date_range[0],
                    'latest': date_range[1]
                }
            
            return stats
            
    except Exception as e:
        logger.error(f"Failed to get database stats: {e}")
        return {"error": str(e)}

# Database management utilities
async def backup_database(backup_path: Path):
    """Create backup of current database"""
    try:
        async with aiosqlite.connect(DB_PATH) as source:
            async with aiosqlite.connect(backup_path) as backup:
                await source.backup(backup)
        logger.info(f"Database backed up to {backup_path}")
    except Exception as e:
        logger.error(f"Database backup failed: {e}")
        raise

async def clear_old_data(days_to_keep: int = 365):
    """Remove data older than specified days to manage database size"""
    try:
        async with db.get_connection() as conn:
            cutoff_date = f"datetime('now', '-{days_to_keep} days')"
            
            # Clear old solar production data
            await conn.execute(f"""
                DELETE FROM solar_production 
                WHERE timestamp < {cutoff_date}
            """)
            
            # Clear old weather data (keep less)
            await conn.execute(f"""
                DELETE FROM weather_data 
                WHERE timestamp < datetime('now', '-180 days')
            """)
            
            await conn.commit()
            logger.info(f"Cleared data older than {days_to_keep} days")
            
    except Exception as e:
        logger.error(f"Failed to clear old data: {e}")
        raise
