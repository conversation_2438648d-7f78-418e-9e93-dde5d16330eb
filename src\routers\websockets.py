from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Depends
from typing import Dict, List
import json
import asyncio

from src.services.deye_service import DeyeAPIService, APIError

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}
        self.data_tasks: Dict[str, asyncio.Task] = {}
        self.deye_service = DeyeAPIService()

    async def connect(self, websocket: WebSocket, station_id: str):
        """Connect client to station data stream"""
        await websocket.accept()
        
        if station_id not in self.active_connections:
            self.active_connections[station_id] = []
            # Start data streaming task for this station
            self.data_tasks[station_id] = asyncio.create_task(
                self._stream_station_data(station_id)
            )
        
        self.active_connections[station_id].append(websocket)

    async def disconnect(self, websocket: WebSocket, station_id: str):
        """Disconnect client from station data stream"""
        if station_id in self.active_connections:
            self.active_connections[station_id].remove(websocket)
            
            # Stop streaming if no more clients
            if not self.active_connections[station_id]:
                del self.active_connections[station_id]
                if station_id in self.data_tasks:
                    self.data_tasks[station_id].cancel()
                    del self.data_tasks[station_id]
        await self.deye_service.close_session()

    async def broadcast_to_station(self, station_id: str, data: dict):
        """Broadcast data to all clients for a station"""
        if station_id in self.active_connections:
            message = json.dumps(data)
            # Create a copy of the list to iterate over, as it might be modified
            connections = list(self.active_connections[station_id])
            for connection in connections:
                try:
                    await connection.send_text(message)
                except WebSocketDisconnect:
                    await self.disconnect(connection, station_id)
                except Exception:
                    # Handle other potential exceptions during send
                    await self.disconnect(connection, station_id)


    async def _stream_station_data(self, station_id: str):
        """Continuously stream real-time data for a station"""
        while station_id in self.active_connections:
            try:
                # Get latest data
                real_time_data = await self.deye_service.get_station_latest_data(station_id)
                
                # Transform field names to camelCase for frontend compatibility
                transformed_data = {
                    "generationPower": real_time_data.get('generationPower', 0.0) or 0.0,
                    "batterySoc": real_time_data.get('batterySOC', 0.0) or 0.0,
                    "batteryPower": real_time_data.get('batteryPower', 0.0) or 0.0,
                    "gridPower": real_time_data.get('wirePower', 0.0) or 0.0,
                    "consumptionPower": real_time_data.get('consumptionPower', 0.0) or 0.0,
                    "irradiateIntensity": real_time_data.get('irradiateIntensity'),
                    "lastUpdateTime": real_time_data.get('lastUpdateTime')
                }
                
                # Broadcast to all connected clients
                await self.broadcast_to_station(station_id, {
                    "type": "real_time_update",
                    "station_id": station_id,
                    "data": transformed_data,
                })
                
                # Wait before next update
                await asyncio.sleep(30)  # 30-second intervals
                
            except APIError as e:
                await self.broadcast_to_station(station_id, {
                    "type": "error",
                    "message": str(e)
                })
                await asyncio.sleep(60)  # Wait longer on error
            except Exception as e:
                # Log error and continue
                print(f"Error streaming data for station {station_id}: {e}")
                await asyncio.sleep(60)  # Wait longer on error

router = APIRouter()
manager = WebSocketManager()

@router.websocket("/solar/{station_id}")
async def solar_data_websocket(
    websocket: WebSocket,
    station_id: str,
):
    """WebSocket endpoint for real-time solar data"""
    await manager.connect(websocket, station_id)
    
    try:
        while True:
            # Keep connection alive and handle client messages
            message = await websocket.receive_text()
            
            # Handle client commands (e.g., change update frequency)
            command = json.loads(message)
            if command.get("type") == "ping":
                await websocket.send_text(json.dumps({"type": "pong"}))
                
    except WebSocketDisconnect:
        await manager.disconnect(websocket, station_id)
    except Exception as e:
        print(f"WebSocket error for station {station_id}: {e}")
        await manager.disconnect(websocket, station_id)
