# FastAPI Backend Architecture for Solar Monitoring

## Executive Summary

FastAPI has been selected as the ideal backend framework for the solar monitoring application based on its exceptional performance, automatic documentation generation, WebSocket support for real-time data, and comprehensive async capabilities. This analysis covers FastAPI's core features, implementation patterns for solar data APIs, real-time WebSocket integration, and production deployment considerations.

## Why FastAPI for Solar Monitoring

### Performance Advantages
- **High Performance**: One of the fastest Python frameworks (comparable to NodeJS and Go)
- **Async Support**: Native asyncio integration for concurrent API calls
- **Type Safety**: Pydantic models for automatic validation and serialization
- **Auto Documentation**: Swagger/OpenAPI docs generated automatically

### Real-Time Capabilities
- **WebSocket Support**: Built-in WebSocket handling for live solar data streaming
- **Background Tasks**: Scheduled data collection and processing
- **Server-Sent Events**: Alternative to WebSockets for real-time updates
- **Dependency Injection**: Clean architecture for shared resources

### Development Benefits
- **Fast Development**: Intuitive API design with Python type hints
- **Editor Support**: Excellent autocomplete and error detection
- **Testing**: Built-in test client and async test support
- **Standards Compliance**: OpenAPI, JSON Schema, OAuth2, and more

## Core Architecture Components

### 1. Application Structure
```
src/
├── main.py                 # FastAPI app entry point
├── config.py              # Configuration management
├── models/                # Pydantic models
│   ├── deye_models.py     # Deye API response models
│   ├── solar_models.py    # Solar data models
│   └── auth_models.py     # Authentication models
├── services/              # Business logic
│   ├── deye_service.py    # Deye API integration
│   ├── auth_service.py    # Authentication service
│   └── data_service.py    # Data processing service
├── routers/               # API endpoints
│   ├── auth.py           # Authentication endpoints
│   ├── solar.py          # Solar data endpoints
│   └── websockets.py     # WebSocket endpoints
└── dependencies.py        # Shared dependencies
```

### 2. FastAPI Application Setup
```python
from fastapi import FastAPI, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from routers import auth, solar, websockets

app = FastAPI(
    title="Solar Monitor API",
    description="Real-time solar inverter monitoring with AI insights",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(solar.router, prefix="/api/solar", tags=["solar-data"])
app.include_router(websockets.router, prefix="/ws", tags=["websockets"])
```

## Data Models with Pydantic

### Solar Station Models
```python
from pydantic import BaseModel, Field
from datetime import datetime
from typing import List, Optional

class SolarStation(BaseModel):
    id: str = Field(..., description="Unique station identifier")
    name: str = Field(..., description="Station display name")
    capacity: float = Field(..., description="Total capacity in kW")
    location: str = Field(..., description="Geographic location")
    status: str = Field(..., description="Current status")
    last_update: datetime = Field(..., description="Last data update")

class RealTimeData(BaseModel):
    station_id: str
    timestamp: datetime
    generation_power: float = Field(..., description="Current solar generation (kW)")
    battery_soc: float = Field(..., description="Battery state of charge (%)")
    battery_power: float = Field(..., description="Battery charge/discharge (kW)")
    grid_power: float = Field(..., description="Grid import/export (kW)")
    consumption_power: float = Field(..., description="Current consumption (kW)")
    irradiate_intensity: float = Field(..., description="Solar irradiation")

class HistoricalData(BaseModel):
    station_id: str
    date: str
    generation_value: float = Field(..., description="Daily generation (kWh)")
    consumption_value: float = Field(..., description="Daily consumption (kWh)")
    grid_value: float = Field(..., description="Net grid exchange (kWh)")
    battery_charge: float = Field(..., description="Battery charged (kWh)")
    battery_discharge: float = Field(..., description="Battery discharged (kWh)")
```

### API Response Models
```python
class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class StationListResponse(APIResponse):
    data: List[SolarStation]

class RealTimeResponse(APIResponse):
    data: RealTimeData

class HistoricalResponse(APIResponse):
    data: List[HistoricalData]
```

## Service Layer Implementation

### Deye API Service
```python
import asyncio
import aiohttp
from typing import List, Optional
from models.deye_models import DeyeStation, DeyeRealTimeData

class DeyeAPIService:
    def __init__(self, base_url: str, app_id: str, app_secret: str):
        self.base_url = base_url
        self.app_id = app_id
        self.app_secret = app_secret
        self.token = None
        self.token_expiry = None
        self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        await self.authenticate()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def authenticate(self) -> str:
        """Authenticate and get access token"""
        if self.token and self.token_expiry > time.time():
            return self.token

        auth_data = {
            "appSecret": self.app_secret,
            "email": self.email,
            "password": self.password_hash
        }

        async with self.session.post(
            f"{self.base_url}/account/token",
            params={"appId": self.app_id},
            json=auth_data
        ) as response:
            result = await response.json()
            
            if result["success"]:
                self.token = result["data"]["token"]
                self.token_expiry = time.time() + result["data"]["expireAt"]
                return self.token
            else:
                raise AuthenticationError(result["msg"])

    async def get_stations(self) -> List[DeyeStation]:
        """Get list of all solar stations"""
        headers = {"Authorization": f"Bearer {await self.authenticate()}"}
        
        async with self.session.get(
            f"{self.base_url}/station/list",
            headers=headers
        ) as response:
            result = await response.json()
            
            if result["success"]:
                return [DeyeStation(**station) for station in result["data"]]
            else:
                raise APIError(result["msg"])

    async def get_real_time_data(self, station_id: str) -> DeyeRealTimeData:
        """Get real-time data for a station"""
        headers = {"Authorization": f"Bearer {await self.authenticate()}"}
        
        async with self.session.post(
            f"{self.base_url}/station/latest",
            headers=headers,
            json={"stationId": station_id}
        ) as response:
            result = await response.json()
            
            if result["success"]:
                return DeyeRealTimeData(**result["data"])
            else:
                raise APIError(result["msg"])
```

### Data Processing Service
```python
from typing import Dict, List
import asyncio
from datetime import datetime, timedelta

class SolarDataService:
    def __init__(self, deye_service: DeyeAPIService):
        self.deye_service = deye_service
        self.cache = {}
        self.cache_ttl = 30  # 30 seconds cache for real-time data

    async def get_dashboard_data(self, station_id: str) -> Dict:
        """Get comprehensive dashboard data"""
        
        # Check cache first
        cache_key = f"dashboard_{station_id}"
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if datetime.now() - timestamp < timedelta(seconds=self.cache_ttl):
                return cached_data

        # Fetch fresh data
        async with self.deye_service as deye:
            real_time = await deye.get_real_time_data(station_id)
            station_info = await deye.get_station_info(station_id)
            
        dashboard_data = {
            "station": station_info,
            "real_time": real_time,
            "timestamp": datetime.now().isoformat(),
            "performance": self._calculate_performance(real_time)
        }

        # Update cache
        self.cache[cache_key] = (dashboard_data, datetime.now())
        return dashboard_data

    def _calculate_performance(self, real_time_data: DeyeRealTimeData) -> Dict:
        """Calculate performance metrics"""
        return {
            "efficiency": self._calculate_efficiency(real_time_data),
            "self_consumption": self._calculate_self_consumption(real_time_data),
            "grid_status": self._determine_grid_status(real_time_data)
        }

    async def get_historical_trends(self, station_id: str, days: int = 30) -> List[Dict]:
        """Get historical data with trend analysis"""
        
        async with self.deye_service as deye:
            historical_data = await deye.get_historical_data(
                station_id, 
                granularity=2,  # Daily
                days_back=days
            )
        
        # Add trend calculations
        trends = []
        for i, day_data in enumerate(historical_data):
            trend_data = {
                **day_data.__dict__,
                "trend": self._calculate_daily_trend(historical_data, i)
            }
            trends.append(trend_data)
            
        return trends
```

## WebSocket Real-Time Data Streaming

### WebSocket Manager
```python
from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, List
import json
import asyncio

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}
        self.data_tasks: Dict[str, asyncio.Task] = {}

    async def connect(self, websocket: WebSocket, station_id: str):
        """Connect client to station data stream"""
        await websocket.accept()
        
        if station_id not in self.active_connections:
            self.active_connections[station_id] = []
            # Start data streaming task for this station
            self.data_tasks[station_id] = asyncio.create_task(
                self._stream_station_data(station_id)
            )
        
        self.active_connections[station_id].append(websocket)

    async def disconnect(self, websocket: WebSocket, station_id: str):
        """Disconnect client from station data stream"""
        if station_id in self.active_connections:
            self.active_connections[station_id].remove(websocket)
            
            # Stop streaming if no more clients
            if not self.active_connections[station_id]:
                del self.active_connections[station_id]
                if station_id in self.data_tasks:
                    self.data_tasks[station_id].cancel()
                    del self.data_tasks[station_id]

    async def broadcast_to_station(self, station_id: str, data: dict):
        """Broadcast data to all clients for a station"""
        if station_id in self.active_connections:
            message = json.dumps(data)
            for connection in self.active_connections[station_id]:
                try:
                    await connection.send_text(message)
                except:
                    # Remove dead connections
                    self.active_connections[station_id].remove(connection)

    async def _stream_station_data(self, station_id: str):
        """Continuously stream real-time data for a station"""
        while station_id in self.active_connections:
            try:
                # Get latest data
                async with DeyeAPIService() as deye:
                    real_time_data = await deye.get_real_time_data(station_id)
                
                # Broadcast to all connected clients
                await self.broadcast_to_station(station_id, {
                    "type": "real_time_update",
                    "station_id": station_id,
                    "data": real_time_data.__dict__,
                    "timestamp": datetime.now().isoformat()
                })
                
                # Wait before next update
                await asyncio.sleep(30)  # 30-second intervals
                
            except Exception as e:
                # Log error and continue
                logger.error(f"Error streaming data for station {station_id}: {e}")
                await asyncio.sleep(60)  # Wait longer on error
```

### WebSocket Endpoints
```python
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from dependencies import get_websocket_manager

router = APIRouter()

@router.websocket("/solar/{station_id}")
async def solar_data_websocket(
    websocket: WebSocket,
    station_id: str,
    manager: WebSocketManager = Depends(get_websocket_manager)
):
    """WebSocket endpoint for real-time solar data"""
    await manager.connect(websocket, station_id)
    
    try:
        while True:
            # Keep connection alive and handle client messages
            message = await websocket.receive_text()
            
            # Handle client commands (e.g., change update frequency)
            command = json.loads(message)
            if command.get("type") == "ping":
                await websocket.send_text(json.dumps({"type": "pong"}))
                
    except WebSocketDisconnect:
        await manager.disconnect(websocket, station_id)
```

## API Endpoints

### Solar Data Endpoints
```python
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import date

router = APIRouter()

@router.get("/stations", response_model=StationListResponse)
async def get_stations(
    solar_service: SolarDataService = Depends(get_solar_service)
):
    """Get list of all solar stations"""
    try:
        stations = await solar_service.get_all_stations()
        return StationListResponse(
            success=True,
            message="Stations retrieved successfully",
            data=stations
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stations/{station_id}/dashboard", response_model=RealTimeResponse)
async def get_station_dashboard(
    station_id: str,
    solar_service: SolarDataService = Depends(get_solar_service)
):
    """Get real-time dashboard data for a station"""
    try:
        dashboard_data = await solar_service.get_dashboard_data(station_id)
        return RealTimeResponse(
            success=True,
            message="Dashboard data retrieved successfully",
            data=dashboard_data
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stations/{station_id}/history", response_model=HistoricalResponse)
async def get_station_history(
    station_id: str,
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    granularity: int = Query(2, description="1=frame, 2=daily, 3=monthly"),
    solar_service: SolarDataService = Depends(get_solar_service)
):
    """Get historical data for a station"""
    try:
        historical_data = await solar_service.get_historical_data(
            station_id, start_date, end_date, granularity
        )
        return HistoricalResponse(
            success=True,
            message="Historical data retrieved successfully",
            data=historical_data
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stations/{station_id}/analytics")
async def get_station_analytics(
    station_id: str,
    period: str = Query("week", regex="^(day|week|month|year)$"),
    solar_service: SolarDataService = Depends(get_solar_service)
):
    """Get AI-powered analytics for a station"""
    try:
        analytics = await solar_service.get_analytics(station_id, period)
        return {
            "success": True,
            "message": "Analytics generated successfully",
            "data": analytics
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## Background Tasks

### Scheduled Data Collection
```python
from fastapi import BackgroundTasks
import asyncio
from datetime import datetime, timedelta

class BackgroundTaskManager:
    def __init__(self, solar_service: SolarDataService):
        self.solar_service = solar_service
        self.running_tasks = {}

    async def start_data_collection(self):
        """Start background data collection tasks"""
        
        # Real-time data collection (every 30 seconds)
        self.running_tasks["real_time"] = asyncio.create_task(
            self._collect_real_time_data()
        )
        
        # Historical data sync (every hour)
        self.running_tasks["historical"] = asyncio.create_task(
            self._sync_historical_data()
        )
        
        # Daily analytics update (every 6 hours)
        self.running_tasks["analytics"] = asyncio.create_task(
            self._update_analytics()
        )

    async def _collect_real_time_data(self):
        """Continuously collect real-time data"""
        while True:
            try:
                stations = await self.solar_service.get_all_stations()
                for station in stations:
                    await self.solar_service.cache_real_time_data(station.id)
                
                await asyncio.sleep(30)  # 30-second intervals
                
            except Exception as e:
                logger.error(f"Error in real-time data collection: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def _sync_historical_data(self):
        """Periodically sync historical data"""
        while True:
            try:
                # Sync yesterday's data for all stations
                yesterday = (datetime.now() - timedelta(days=1)).date()
                stations = await self.solar_service.get_all_stations()
                
                for station in stations:
                    await self.solar_service.sync_daily_data(
                        station.id, yesterday
                    )
                
                await asyncio.sleep(3600)  # 1 hour intervals
                
            except Exception as e:
                logger.error(f"Error in historical data sync: {e}")
                await asyncio.sleep(1800)  # Wait 30 min on error

    async def _update_analytics(self):
        """Update AI analytics periodically"""
        while True:
            try:
                stations = await self.solar_service.get_all_stations()
                for station in stations:
                    await self.solar_service.update_analytics(station.id)
                
                await asyncio.sleep(21600)  # 6 hour intervals
                
            except Exception as e:
                logger.error(f"Error in analytics update: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour on error
```

## Configuration Management

### Environment Configuration
```python
from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # API Configuration
    api_title: str = "Solar Monitor API"
    api_version: str = "1.0.0"
    debug: bool = False
    
    # Deye API Settings
    deye_base_url: str = "https://eu1-developer.deyecloud.com/v1.0"
    deye_app_id: str
    deye_app_secret: str
    deye_email: str
    deye_password: str
    
    # Database (if needed)
    database_url: Optional[str] = None
    
    # Redis (for caching)
    redis_url: Optional[str] = "redis://localhost:6379"
    
    # CORS Settings
    cors_origins: str = "http://localhost:3000"
    
    # Authentication
    secret_key: str
    access_token_expire_minutes: int = 30
    
    class Config:
        env_file = ".env"

settings = Settings()
```

## Testing Strategy

### Unit Tests
```python
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch

@pytest.fixture
def client():
    from main import app
    return TestClient(app)

@pytest.fixture
def mock_deye_service():
    with patch('services.deye_service.DeyeAPIService') as mock:
        mock_instance = AsyncMock()
        mock.return_value.__aenter__.return_value = mock_instance
        yield mock_instance

class TestSolarEndpoints:
    def test_get_stations(self, client, mock_deye_service):
        mock_deye_service.get_stations.return_value = [
            {"id": "station1", "name": "Test Station"}
        ]
        
        response = client.get("/api/solar/stations")
        assert response.status_code == 200
        assert response.json()["success"] is True

    def test_get_real_time_data(self, client, mock_deye_service):
        mock_deye_service.get_real_time_data.return_value = {
            "generation_power": 5.2,
            "battery_soc": 85.0,
            "grid_power": -2.1
        }
        
        response = client.get("/api/solar/stations/station1/dashboard")
        assert response.status_code == 200
        
    @pytest.mark.asyncio
    async def test_websocket_connection(self, client):
        with client.websocket_connect("/ws/solar/station1") as websocket:
            data = websocket.receive_json()
            assert "type" in data
```

## Production Deployment

### Docker Configuration
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose for Local Development
```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEYE_APP_ID=${DEYE_APP_ID}
      - DEYE_APP_SECRET=${DEYE_APP_SECRET}
      - DEYE_EMAIL=${DEYE_EMAIL}
      - DEYE_PASSWORD=${DEYE_PASSWORD}
    volumes:
      - .:/app
    depends_on:
      - redis

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - api
```

## Performance Optimizations

### Async Best Practices
1. **Connection Pooling**: Use aiohttp session for Deye API calls
2. **Caching Strategy**: Redis for frequently accessed data
3. **Rate Limiting**: Respect API limits with intelligent queuing
4. **Error Handling**: Circuit breaker pattern for API failures
5. **Memory Management**: Proper cleanup of WebSocket connections

### Monitoring and Logging
```python
import structlog
from fastapi import Request
import time

# Structured logging
logger = structlog.get_logger()

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    logger.info(
        "request_processed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time
    )
    
    return response
```

## Conclusion

FastAPI provides an excellent foundation for building a high-performance, real-time solar monitoring backend. Key advantages for this project:

**Core Strengths:**
- **Real-time Capabilities**: WebSockets for live data streaming
- **Performance**: Async architecture for concurrent API operations  
- **Developer Experience**: Auto-generated docs and type safety
- **Scalability**: Built for production with proper caching and background tasks

**Implementation Highlights:**
- Clean service layer architecture for Deye API integration
- Pydantic models for data validation and serialization
- WebSocket manager for real-time client connections
- Background tasks for automated data collection
- Comprehensive error handling and logging

**Production Ready Features:**
- Docker containerization for easy deployment
- Environment-based configuration management
- Structured logging and monitoring
- Unit and integration test framework
- CORS and security middleware

This architecture provides a solid foundation for building the solar monitoring application with room for future enhancements like machine learning analytics, user management, and advanced data visualization features.
