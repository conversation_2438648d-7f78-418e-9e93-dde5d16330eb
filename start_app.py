#!/usr/bin/env python3
"""
Startup script for Solar Display Application
This script starts both the FastAPI backend and frontend server
"""

import subprocess
import sys
import time
import signal
import os
from pathlib import Path

def start_backend():
    """Start the FastAPI backend server"""
    print("Starting FastAPI backend server...")
    
    # Use virtual environment Python if available
    venv_python = Path(__file__).parent / "venv" / "Scripts" / "python.exe"
    python_exe = str(venv_python) if venv_python.exists() else sys.executable
    
    backend_cmd = [
        python_exe, "-m", "uvicorn", 
        "src.main:app", 
        "--reload", 
        "--host", "0.0.0.0", 
        "--port", "8000"
    ]
    return subprocess.Popen(backend_cmd, cwd=Path(__file__).parent)

def start_frontend():
    """Start the frontend server"""
    print("Starting frontend server...")
    
    # Use virtual environment Python if available
    venv_python = Path(__file__).parent / "venv" / "Scripts" / "python.exe"
    python_exe = str(venv_python) if venv_python.exists() else sys.executable
    
    frontend_cmd = [python_exe, "serve_frontend.py"]
    return subprocess.Popen(frontend_cmd, cwd=Path(__file__).parent)

def main():
    print("=" * 60)
    print("Solar Display Application Startup")
    print("=" * 60)
    
    # Check if .env file exists
    env_file = Path(__file__).parent / ".env"
    if not env_file.exists():
        print("⚠️  WARNING: .env file not found!")
        print("Please create .env file with your Deye API credentials:")
        print("DEYE_APP_ID=your_app_id")
        print("DEYE_APP_SECRET=your_app_secret") 
        print("DEYE_EMAIL=your_email")
        print("DEYE_PASSWORD=your_password")
        print("ACCESS_CODE=1234")
        print()
    
    processes = []
    
    try:
        # Start backend
        backend_process = start_backend()
        processes.append(backend_process)
        time.sleep(2)  # Give backend time to start
        
        # Start frontend
        frontend_process = start_frontend()
        processes.append(frontend_process)
        time.sleep(1)
        
        print()
        print("=" * 60)
        print("🚀 Solar Display Application Started Successfully!")
        print("=" * 60)
        print("📊 Frontend Dashboard: http://localhost:3000")
        print("🔧 Backend API Docs:   http://localhost:8000/api/docs")
        print("💾 Backend API:        http://localhost:8000/api")
        print()
        print("📝 Default access code: 1234")
        print("   (Change in .env file: ACCESS_CODE=your_code)")
        print()
        print("🛑 Press Ctrl+C to stop all servers")
        print("=" * 60)
        
        # Wait for processes
        while True:
            time.sleep(1)
            # Check if any process has died
            for process in processes:
                if process.poll() is not None:
                    print(f"⚠️  Process {process.pid} has stopped")
                    raise KeyboardInterrupt
                    
    except KeyboardInterrupt:
        print("\n🛑 Shutting down servers...")
        
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
        
        print("✅ All servers stopped successfully!")

if __name__ == "__main__":
    main()
