"""
ML Feature Engineering Service for Solar Display Application

Advanced feature engineering for solar energy production forecasting and anomaly detection.
Transforms raw solar and weather data into ML-ready features optimized for South African
solar patterns in Villieria, Pretoria.

Features generated:
- Temporal features (cyclical encoding, seasonal patterns)
- Weather-solar correlations  
- Efficiency metrics and degradation indicators
- Rolling averages and trend analysis
- Lag features for time-series modeling
"""

import numpy as np
import pandas as pd
import math
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from src.database import (
    db, SolarProductionData, WeatherData, MLFeatures,
    get_season_from_date, get_time_of_day, categorize_weather,
    Season, TimeOfDay, WeatherCategory
)

logger = logging.getLogger(__name__)

@dataclass
class FeatureEngineeeringConfig:
    """Configuration for feature engineering"""
    rolling_window_hours: int = 24  # For moving averages
    lag_hours: List[int] = None  # Lag features (1, 3, 6, 24 hours)
    min_samples_for_trends: int = 48  # Minimum samples for trend analysis
    
    def __post_init__(self):
        if self.lag_hours is None:
            self.lag_hours = [1, 3, 6, 24]

class SolarFeatureEngineering:
    """
    Advanced feature engineering for solar energy ML models
    
    Generates features that capture:
    - South African seasonal solar patterns
    - Weather-production correlations
    - System efficiency trends
    - Time-based cyclical patterns
    """
    
    def __init__(self):
        self.config = FeatureEngineeeringConfig()
        
    async def create_training_features(self, days_back: int = 90) -> pd.DataFrame:
        """
        Create comprehensive ML training features from historical data
        
        Args:
            days_back: Number of days of historical data to process
            
        Returns:
            DataFrame with engineered features for ML training
        """
        logger.info(f"Creating ML training features for {days_back} days of data")
        
        try:
            # Get historical data from database
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days_back)
            
            # Fetch solar production data
            solar_data = await self._fetch_solar_data(start_time, end_time)
            if len(solar_data) < self.config.min_samples_for_trends:
                logger.warning(f"Insufficient solar data for feature engineering: {len(solar_data)} samples")
                return pd.DataFrame()
            
            # Fetch weather data
            weather_data = await self._fetch_weather_data(start_time, end_time)
            
            # Merge solar and weather data
            df = self._merge_solar_weather_data(solar_data, weather_data)
            
            # Generate features
            df = self._add_temporal_features(df)
            df = self._add_weather_features(df)
            df = self._add_solar_features(df)
            df = self._add_efficiency_features(df)
            df = self._add_lag_features(df)
            df = self._add_rolling_features(df)
            df = self._add_trend_features(df)
            df = self._add_interaction_features(df)
            
            # Clean and validate features
            df = self._clean_features(df)
            
            logger.info(f"Created {len(df)} training samples with {len(df.columns)} features")
            return df
            
        except Exception as e:
            logger.error(f"Failed to create training features: {e}")
            raise
    
    async def create_inference_features(self, current_solar: SolarProductionData, 
                                      current_weather: Optional[WeatherData],
                                      forecast_weather: Optional[List[WeatherData]] = None) -> Dict[str, Any]:
        """
        Create features for real-time ML inference
        
        Args:
            current_solar: Current solar production data
            current_weather: Current weather data
            forecast_weather: Weather forecast data (optional)
            
        Returns:
            Dictionary of features for ML inference
        """
        try:
            timestamp = current_solar.timestamp
            
            # Basic temporal features
            features = self._create_temporal_features(timestamp)
            
            # Current solar features
            features.update(self._create_solar_features(current_solar))
            
            # Current weather features
            if current_weather:
                features.update(self._create_weather_features(current_weather))
            else:
                # Use estimated weather features
                features.update(self._estimate_weather_features(timestamp, current_solar))
            
            # Historical context features
            historical_features = await self._create_historical_context_features(timestamp)
            features.update(historical_features)
            
            # Forecast features (if available)
            if forecast_weather:
                forecast_features = self._create_forecast_features(forecast_weather)
                features.update(forecast_features)
            
            return features
            
        except Exception as e:
            logger.error(f"Failed to create inference features: {e}")
            return {}
    
    # =================== DATA FETCHING ===================
    
    async def _fetch_solar_data(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """Fetch solar production data from database"""
        return await db.execute_query(
            """SELECT * FROM solar_production 
               WHERE timestamp BETWEEN ? AND ?
               ORDER BY timestamp""",
            (start_time.isoformat(), end_time.isoformat())
        )
    
    async def _fetch_weather_data(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """Fetch weather data from database"""
        return await db.execute_query(
            """SELECT * FROM weather_data
               WHERE timestamp BETWEEN ? AND ?
               ORDER BY timestamp""",
            (start_time.isoformat(), end_time.isoformat())
        )
    
    def _merge_solar_weather_data(self, solar_data: List[Dict], weather_data: List[Dict]) -> pd.DataFrame:
        """Merge solar and weather data on timestamp"""
        # Convert to DataFrames
        df_solar = pd.DataFrame(solar_data)
        df_weather = pd.DataFrame(weather_data)
        
        if df_solar.empty:
            return pd.DataFrame()
        
        # Convert timestamps
        df_solar['timestamp'] = pd.to_datetime(df_solar['timestamp'])
        if not df_weather.empty:
            df_weather['timestamp'] = pd.to_datetime(df_weather['timestamp'])
            
            # Merge on timestamp (outer join to keep all solar data)
            df = pd.merge(df_solar, df_weather, on='timestamp', how='left', suffixes=('', '_weather'))
        else:
            df = df_solar.copy()
            # Add empty weather columns
            weather_columns = ['temperature', 'humidity', 'solar_radiation', 'cloud_cover', 'wind_speed', 'pressure', 'weather_code']
            for col in weather_columns:
                df[col] = np.nan
        
        # Set timestamp as index
        df = df.set_index('timestamp').sort_index()
        
        return df
    
    # =================== FEATURE ENGINEERING ===================
    
    def _add_temporal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features with cyclical encoding"""
        if df.empty:
            return df
        
        # Basic time features
        df['hour'] = df.index.hour
        df['day_of_week'] = df.index.dayofweek
        df['day_of_year'] = df.index.dayofyear
        df['month'] = df.index.month
        df['quarter'] = df.index.quarter
        df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
        
        # Cyclical encoding for periodic features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        df['day_of_week_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['day_of_week_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        
        # South African seasons
        df['season'] = df.index.map(get_season_from_date)
        df['season_encoded'] = df['season'].map({
            Season.SUMMER: 0, Season.AUTUMN: 1, Season.WINTER: 2, Season.SPRING: 3
        })
        
        # Time of day categories
        df['time_of_day'] = df.index.map(get_time_of_day)
        df['time_of_day_encoded'] = df['time_of_day'].map({
            TimeOfDay.NIGHT: 0, TimeOfDay.MORNING: 1, TimeOfDay.MIDDAY: 2, 
            TimeOfDay.AFTERNOON: 3, TimeOfDay.EVENING: 4
        })
        
        # Solar elevation angle (approximate for Pretoria)
        df['solar_elevation'] = self._calculate_solar_elevation(df.index)
        df['daylight_hours'] = self._calculate_daylight_hours(df.index)
        
        return df
    
    def _add_weather_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add weather-related features"""
        if df.empty:
            return df
        
        # Fill missing weather data with interpolation
        weather_cols = ['temperature', 'humidity', 'solar_radiation', 'cloud_cover', 'wind_speed']
        for col in weather_cols:
            if col in df.columns:
                df[col] = df[col].interpolate(method='linear', limit=6)  # Max 6 hour gap
        
        # Weather categorization
        if 'cloud_cover' in df.columns and 'weather_code' in df.columns:
            df['weather_category'] = df.apply(
                lambda row: categorize_weather(row.get('cloud_cover', 50), row.get('weather_code')),
                axis=1
            )
        else:
            df['weather_category'] = WeatherCategory.PARTLY_CLOUDY
        
        # Weather feature engineering
        if 'temperature' in df.columns:
            df['temp_feel'] = self._calculate_feels_like_temp(df)
            df['temp_dewpoint'] = self._calculate_dewpoint(df)
        
        # Solar radiation features
        if 'solar_radiation' in df.columns:
            df['solar_radiation_ratio'] = df['solar_radiation'] / (df['solar_elevation'] + 0.1)
            df['clear_sky_index'] = self._calculate_clear_sky_index(df)
        
        # Wind chill effect on solar panels
        if 'wind_speed' in df.columns and 'temperature' in df.columns:
            df['panel_temp_estimate'] = self._estimate_panel_temperature(df)
        
        return df
    
    def _add_solar_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add solar production features"""
        if df.empty:
            return df
        
        # Power density and efficiency metrics
        if 'total_power' in df.columns:
            # Specific power (power per unit irradiance)
            if 'solar_radiation' in df.columns:
                df['specific_power'] = df['total_power'] / (df['solar_radiation'] + 1)  # Avoid div by zero
                df['power_irradiance_ratio'] = df['total_power'] / (df['irradiate_intensity'].fillna(df['solar_radiation']) + 1)
            
            # Performance ratio
            df['performance_ratio'] = self._calculate_performance_ratio(df)
            
            # Capacity factor
            rated_capacity = 5.0  # Assume 5kW system (adjust as needed)
            df['capacity_factor'] = df['total_power'] / rated_capacity
        
        # Energy metrics
        if 'daily_energy' in df.columns:
            df['hourly_energy_rate'] = df['daily_energy'].diff()  # Energy increment per hour
        
        # System health indicators
        if 'efficiency' in df.columns:
            df['efficiency_normalized'] = df['efficiency'].fillna(0) / 100.0
        
        # Battery metrics (if available)
        if 'battery_soc' in df.columns:
            df['battery_trend'] = df['battery_soc'].diff()
            df['battery_cycling'] = abs(df['battery_power'].fillna(0))
        
        return df
    
    def _add_efficiency_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add system efficiency and degradation features"""
        if df.empty or 'total_power' not in df.columns:
            return df
        
        # Expected vs actual production
        df['expected_power'] = self._calculate_expected_power(df)
        df['power_deficit'] = df['expected_power'] - df['total_power']
        df['efficiency_ratio'] = df['total_power'] / (df['expected_power'] + 0.1)
        
        # Degradation indicators
        df['power_per_irradiance'] = df['total_power'] / (df.get('solar_radiation', df.get('irradiate_intensity', 1)).fillna(1) + 1)
        
        # Temperature-corrected efficiency
        if 'temperature' in df.columns:
            df['temp_corrected_efficiency'] = self._temperature_correct_efficiency(df)
        
        return df
    
    def _add_lag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add lagged features for time series modeling"""
        if df.empty:
            return df
        
        # Features to lag
        lag_features = ['total_power', 'solar_radiation', 'temperature', 'cloud_cover']
        
        for feature in lag_features:
            if feature in df.columns:
                for lag in self.config.lag_hours:
                    df[f'{feature}_lag_{lag}h'] = df[feature].shift(lag)
        
        return df
    
    def _add_rolling_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add rolling window features"""
        if df.empty:
            return df
        
        window = self.config.rolling_window_hours
        
        # Rolling statistics for key features
        features = ['total_power', 'solar_radiation', 'temperature', 'efficiency']
        
        for feature in features:
            if feature in df.columns:
                df[f'{feature}_rolling_mean_{window}h'] = df[feature].rolling(window=window, min_periods=6).mean()
                df[f'{feature}_rolling_std_{window}h'] = df[feature].rolling(window=window, min_periods=6).std()
                df[f'{feature}_rolling_max_{window}h'] = df[feature].rolling(window=window, min_periods=6).max()
                df[f'{feature}_rolling_min_{window}h'] = df[feature].rolling(window=window, min_periods=6).min()
        
        # Rolling correlations
        if 'total_power' in df.columns and 'solar_radiation' in df.columns:
            df['power_radiation_corr'] = df['total_power'].rolling(window=window).corr(df['solar_radiation'])
        
        return df
    
    def _add_trend_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add trend analysis features"""
        if df.empty or len(df) < self.config.min_samples_for_trends:
            return df
        
        # Linear trend over different windows
        windows = [24, 72, 168]  # 1 day, 3 days, 1 week
        
        for window in windows:
            if len(df) >= window:
                # Power trend
                if 'total_power' in df.columns:
                    df[f'power_trend_{window}h'] = self._calculate_linear_trend(df['total_power'], window)
                
                # Efficiency trend
                if 'efficiency' in df.columns:
                    df[f'efficiency_trend_{window}h'] = self._calculate_linear_trend(df['efficiency'], window)
        
        return df
    
    def _add_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add interaction features between variables"""
        if df.empty:
            return df
        
        # Weather-time interactions
        if 'temperature' in df.columns:
            df['temp_hour_interaction'] = df['temperature'] * df['hour_sin']
            df['temp_season_interaction'] = df['temperature'] * df['season_encoded']
        
        # Solar-weather interactions
        if 'solar_radiation' in df.columns and 'cloud_cover' in df.columns:
            df['radiation_cloud_interaction'] = df['solar_radiation'] * (100 - df['cloud_cover']) / 100
        
        # Power-efficiency interactions
        if 'total_power' in df.columns and 'efficiency' in df.columns:
            df['power_efficiency_product'] = df['total_power'] * df['efficiency']
        
        return df
    
    def _clean_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate features"""
        if df.empty:
            return df
        
        # Remove infinite values
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # Forward fill then backward fill for small gaps
        df = df.fillna(method='ffill', limit=3).fillna(method='bfill', limit=3)
        
        # Drop rows with too many missing values
        threshold = len(df.columns) * 0.7  # Keep rows with at least 70% valid data
        df = df.dropna(thresh=threshold)
        
        # Cap outliers (values beyond 3 standard deviations)
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col not in ['timestamp', 'station_id']:  # Skip non-feature columns
                mean = df[col].mean()
                std = df[col].std()
                if std > 0:
                    lower_bound = mean - 3 * std
                    upper_bound = mean + 3 * std
                    df[col] = df[col].clip(lower=lower_bound, upper=upper_bound)
        
        return df
    
    # =================== HELPER METHODS ===================
    
    def _create_temporal_features(self, timestamp: datetime) -> Dict[str, float]:
        """Create temporal features for a single timestamp"""
        hour_angle = 2 * math.pi * timestamp.hour / 24
        day_angle = 2 * math.pi * timestamp.timetuple().tm_yday / 365
        
        return {
            'hour': timestamp.hour,
            'day_of_week': timestamp.weekday(),
            'day_of_year': timestamp.timetuple().tm_yday,
            'month': timestamp.month,
            'is_weekend': 1 if timestamp.weekday() >= 5 else 0,
            'hour_sin': math.sin(hour_angle),
            'hour_cos': math.cos(hour_angle),
            'day_of_year_sin': math.sin(day_angle),
            'day_of_year_cos': math.cos(day_angle),
            'season_encoded': list(Season).index(get_season_from_date(timestamp)),
            'time_of_day_encoded': list(TimeOfDay).index(get_time_of_day(timestamp)),
            'solar_elevation': self._calculate_solar_elevation_single(timestamp),
        }
    
    def _create_solar_features(self, solar_data: SolarProductionData) -> Dict[str, float]:
        """Create solar features from current data"""
        return {
            'total_power': solar_data.total_power,
            'daily_energy': solar_data.daily_energy,
            'battery_soc': solar_data.battery_soc or 0.0,
            'battery_power': solar_data.battery_power or 0.0,
            'grid_power': solar_data.grid_power or 0.0,
            'consumption_power': solar_data.consumption_power or 0.0,
            'efficiency': solar_data.efficiency or 0.0,
            'irradiate_intensity': solar_data.irradiate_intensity or 0.0,
        }
    
    def _create_weather_features(self, weather_data: WeatherData) -> Dict[str, float]:
        """Create weather features from current data"""
        return {
            'temperature': weather_data.temperature,
            'humidity': weather_data.humidity,
            'solar_radiation': weather_data.solar_radiation,
            'cloud_cover': weather_data.cloud_cover,
            'wind_speed': weather_data.wind_speed,
            'pressure': weather_data.pressure or 1013.25,
            'weather_code': weather_data.weather_code or 0,
        }
    
    def _estimate_weather_features(self, timestamp: datetime, solar_data: SolarProductionData) -> Dict[str, float]:
        """Estimate weather features when weather data is not available"""
        # Simple estimation based on solar production and time
        hour = timestamp.hour
        
        # Estimate temperature (Pretoria winter patterns)
        if 6 <= hour <= 18:
            temp = 15 + 8 * math.sin((hour - 6) * math.pi / 12)
        else:
            temp = 12
        
        # Estimate irradiance based on solar production
        irradiance = (solar_data.irradiate_intensity or 0.0) if solar_data.irradiate_intensity else solar_data.total_power * 200
        
        return {
            'temperature': temp,
            'humidity': 50.0,
            'solar_radiation': irradiance,
            'cloud_cover': max(0, 100 - (irradiance / 6)),  # Inverse relationship
            'wind_speed': 2.0,
            'pressure': 1013.25,
            'weather_code': 1,  # Fair weather
        }
    
    async def _create_historical_context_features(self, timestamp: datetime) -> Dict[str, float]:
        """Create features based on recent historical data"""
        try:
            # Get data from last 24 hours for context
            start_time = timestamp - timedelta(hours=24)
            
            recent_solar = await self._fetch_solar_data(start_time, timestamp)
            recent_weather = await self._fetch_weather_data(start_time, timestamp)
            
            if not recent_solar:
                return {}
            
            df = pd.DataFrame(recent_solar)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Recent averages and trends
            features = {}
            
            if len(df) > 0:
                features['recent_avg_power'] = df['total_power'].mean()
                features['recent_max_power'] = df['total_power'].max()
                features['recent_avg_efficiency'] = df['efficiency'].fillna(0).mean()
                
                # Power trend over last 24 hours
                if len(df) >= 6:
                    features['power_trend_24h'] = self._calculate_linear_trend_simple(df['total_power'].values)
            
            return features
            
        except Exception as e:
            logger.warning(f"Could not create historical context features: {e}")
            return {}
    
    def _create_forecast_features(self, forecast_weather: List[WeatherData]) -> Dict[str, float]:
        """Create features from weather forecast"""
        if not forecast_weather:
            return {}
        
        # Next 6 hours forecast summary
        next_6h = forecast_weather[:6]
        
        return {
            'forecast_avg_temp_6h': np.mean([w.temperature for w in next_6h]),
            'forecast_avg_radiation_6h': np.mean([w.solar_radiation for w in next_6h]),
            'forecast_avg_cloud_6h': np.mean([w.cloud_cover for w in next_6h]),
            'forecast_max_radiation_6h': max([w.solar_radiation for w in next_6h]),
            'forecast_min_cloud_6h': min([w.cloud_cover for w in next_6h]),
        }
    
    def _calculate_solar_elevation(self, timestamps: pd.DatetimeIndex) -> pd.Series:
        """Calculate solar elevation angle for Pretoria"""
        # Simplified solar elevation calculation
        lat = math.radians(-25.749)  # Pretoria latitude
        
        elevations = []
        for ts in timestamps:
            day_of_year = ts.timetuple().tm_yday
            hour_angle = (ts.hour - 12) * 15 * math.pi / 180
            
            # Solar declination
            declination = 23.45 * math.sin(math.radians(360 * (284 + day_of_year) / 365))
            declination = math.radians(declination)
            
            # Solar elevation
            elevation = math.asin(
                math.sin(lat) * math.sin(declination) +
                math.cos(lat) * math.cos(declination) * math.cos(hour_angle)
            )
            
            elevations.append(max(0, math.degrees(elevation)))
        
        return pd.Series(elevations, index=timestamps)
    
    def _calculate_solar_elevation_single(self, timestamp: datetime) -> float:
        """Calculate solar elevation for single timestamp"""
        return self._calculate_solar_elevation(pd.DatetimeIndex([timestamp])).iloc[0]
    
    def _calculate_daylight_hours(self, timestamps: pd.DatetimeIndex) -> pd.Series:
        """Calculate daylight hours for each day"""
        # Simplified daylight calculation for Pretoria
        daylight_hours = []
        for ts in timestamps:
            day_of_year = ts.timetuple().tm_yday
            # Pretoria daylight variation (approximate)
            daylight = 12 + 2.5 * math.sin(2 * math.pi * (day_of_year - 81) / 365)
            daylight_hours.append(daylight)
        
        return pd.Series(daylight_hours, index=timestamps)
    
    def _calculate_feels_like_temp(self, df: pd.DataFrame) -> pd.Series:
        """Calculate feels-like temperature"""
        if 'temperature' not in df.columns or 'humidity' not in df.columns:
            return df.get('temperature', pd.Series(index=df.index, dtype=float))
        
        # Simplified heat index calculation
        temp = df['temperature']
        humidity = df['humidity']
        
        # Only adjust for high temperatures and humidity
        adjustment = np.where(
            (temp > 25) & (humidity > 70),
            (temp - 25) * (humidity - 70) * 0.01,
            0
        )
        
        return temp + adjustment
    
    def _calculate_dewpoint(self, df: pd.DataFrame) -> pd.Series:
        """Calculate dew point temperature"""
        if 'temperature' not in df.columns or 'humidity' not in df.columns:
            return pd.Series(index=df.index, dtype=float)
        
        # Magnus formula approximation
        temp = df['temperature']
        humidity = df['humidity']
        
        a = 17.27
        b = 237.7
        
        gamma = (a * temp / (b + temp)) + np.log(humidity / 100)
        dewpoint = (b * gamma) / (a - gamma)
        
        return dewpoint
    
    def _calculate_clear_sky_index(self, df: pd.DataFrame) -> pd.Series:
        """Calculate clear sky index (ratio of actual to theoretical clear sky irradiance)"""
        if 'solar_radiation' not in df.columns or 'solar_elevation' not in df.columns:
            return pd.Series(index=df.index, dtype=float)
        
        # Theoretical clear sky irradiance (simplified)
        clear_sky_radiation = 1000 * np.sin(np.radians(df['solar_elevation'].clip(0, 90)))
        clear_sky_radiation = clear_sky_radiation.clip(1, 1000)  # Avoid division by zero
        
        return df['solar_radiation'] / clear_sky_radiation
    
    def _estimate_panel_temperature(self, df: pd.DataFrame) -> pd.Series:
        """Estimate solar panel temperature based on ambient conditions"""
        if 'temperature' not in df.columns:
            return pd.Series(index=df.index, dtype=float)
        
        temp = df['temperature']
        radiation = df.get('solar_radiation', 0)
        wind_speed = df.get('wind_speed', 2.0)
        
        # NOCT model approximation
        # Panel temp = Ambient + (NOCT - 20) * (Irradiance / 800) * (1 - efficiency/100)
        noct = 45  # Nominal Operating Cell Temperature
        efficiency = 0.2  # 20% efficiency assumption
        
        temp_rise = (noct - 20) * (radiation / 800) * (1 - efficiency)
        wind_cooling = wind_speed * 2  # Wind cooling effect
        
        panel_temp = temp + temp_rise - wind_cooling
        return panel_temp
    
    def _calculate_performance_ratio(self, df: pd.DataFrame) -> pd.Series:
        """Calculate performance ratio (actual / theoretical output)"""
        if 'total_power' not in df.columns:
            return pd.Series(index=df.index, dtype=float)
        
        expected_power = self._calculate_expected_power(df)
        return df['total_power'] / (expected_power + 0.1)  # Avoid division by zero
    
    def _calculate_expected_power(self, df: pd.DataFrame) -> pd.Series:
        """Calculate expected power output based on conditions"""
        rated_capacity = 5.0  # kW (adjust based on actual system)
        standard_irradiance = 1000  # W/m²
        
        # Use solar radiation or irradiate intensity
        irradiance = df.get('solar_radiation', df.get('irradiate_intensity', 0))
        
        # Basic power calculation
        expected_power = rated_capacity * (irradiance / standard_irradiance)
        
        # Temperature correction (panels lose efficiency in heat)
        if 'temperature' in df.columns:
            temp_coeff = -0.004  # %/°C
            standard_temp = 25  # °C
            temp_factor = 1 + temp_coeff * (df['temperature'] - standard_temp)
            expected_power *= temp_factor
        
        return expected_power.clip(0, rated_capacity * 1.2)  # Cap at 120% of rated
    
    def _temperature_correct_efficiency(self, df: pd.DataFrame) -> pd.Series:
        """Calculate temperature-corrected efficiency"""
        if 'efficiency' not in df.columns or 'temperature' not in df.columns:
            return df.get('efficiency', pd.Series(index=df.index, dtype=float))
        
        # Correct efficiency for temperature effects
        temp_coeff = -0.004  # %/°C efficiency loss
        standard_temp = 25  # °C
        
        correction_factor = 1 - temp_coeff * (df['temperature'] - standard_temp)
        return df['efficiency'] / (correction_factor + 0.01)  # Avoid division by zero
    
    def _calculate_linear_trend(self, series: pd.Series, window: int) -> pd.Series:
        """Calculate linear trend over rolling window"""
        def trend_slope(values):
            if len(values) < 3:
                return 0
            x = np.arange(len(values))
            return np.polyfit(x, values, 1)[0]  # Slope of linear fit
        
        return series.rolling(window=window, min_periods=window//2).apply(trend_slope)
    
    def _calculate_linear_trend_simple(self, values: np.ndarray) -> float:
        """Calculate simple linear trend for array of values"""
        if len(values) < 3:
            return 0.0
        
        x = np.arange(len(values))
        try:
            slope, _ = np.polyfit(x, values, 1)
            return slope
        except:
            return 0.0

# Global instance
feature_engineering_service = SolarFeatureEngineering()

async def get_feature_engineering_service() -> SolarFeatureEngineering:
    """Dependency injection for feature engineering service"""
    return feature_engineering_service
