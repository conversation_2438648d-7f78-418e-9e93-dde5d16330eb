# Solar Display PRD - Production Requirements Document
## Comprehensive ML Training, Database & Weather API Integration

**Location:** Villieria, Pretoria, South Africa  
**Coordinates:** -25.749°S, 28.231°E  
**Phase:** 3-4 Implementation Plan  
**Document Version:** 1.0

---

## Executive Summary

This PRD outlines the comprehensive implementation strategy for transforming the Solar Display application from a Phase 1/2 monitoring system into a production-ready AI-powered solar analytics platform. The current system successfully displays real-time Deye inverter data but relies on mock ML models that return "NaN%" values. This document provides a research-backed roadmap for implementing proper database infrastructure, ML training pipelines, and weather API integration for location-specific solar forecasting in South Africa.

---

## Current System Analysis

### Working Components ✅
- **FastAPI Backend**: Fully functional API with auth, solar data routes, WebSocket support
- **Frontend Interface**: Real-time dashboard with charts, metrics display, responsive design
- **Deye API Integration**: Live inverter data retrieval working correctly
- **ML Framework**: scikit-learn ensemble models implemented (not trained)

### Critical Issues ❌
- **Mock Data Generation**: ML service using simulated patterns instead of learning from real data
- **NaN Values**: AI insights showing "NaN%" efficiency metrics due to insufficient historical data
- **No Data Persistence**: No database for historical pattern learning
- **Generic Recommendations**: AI providing generic tips instead of system-specific insights

---

## Phase 3-4 Implementation Strategy

## 1. Database Infrastructure Implementation

### Primary Database: SQLite + TimescaleDB Architecture

#### SQLite (Development & Local Storage)
```sql
-- Solar Production Data
CREATE TABLE solar_production (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    total_power REAL,           -- Total system power (kW)
    daily_energy REAL,          -- Daily energy production (kWh)
    inverter_temp REAL,         -- Inverter temperature (°C)
    dc_voltage REAL,            -- DC input voltage (V)
    ac_voltage REAL,            -- AC output voltage (V)
    efficiency REAL,            -- System efficiency (%)
    grid_frequency REAL,        -- Grid frequency (Hz)
    error_code TEXT,            -- Any error codes
    raw_data TEXT              -- Complete JSON from Deye API
);

-- Weather Data Cache
CREATE TABLE weather_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    temperature REAL,           -- Air temperature (°C)
    humidity REAL,              -- Relative humidity (%)
    solar_radiation REAL,       -- Solar irradiance (W/m²)
    cloud_cover REAL,          -- Cloud coverage (%)
    wind_speed REAL,           -- Wind speed (m/s)
    pressure REAL,             -- Atmospheric pressure (hPa)
    weather_code INTEGER,      -- WMO weather code
    source TEXT DEFAULT 'open-meteo'
);

-- ML Training Features
CREATE TABLE ml_features (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME,
    production_kw REAL,
    irradiance_wm2 REAL,
    temperature_c REAL,
    cloud_cover_pct REAL,
    efficiency_pct REAL,
    season TEXT,               -- Summer/Winter/Autumn/Spring
    time_of_day TEXT,         -- Morning/Midday/Afternoon/Evening
    weather_category TEXT     -- Clear/Cloudy/Overcast/Rainy
);


```

#### TimescaleDB (Production Time-Series)
For production deployment, migrate to TimescaleDB for optimized time-series operations:
```sql
-- Convert to hypertables for time-series optimization
SELECT create_hypertable('solar_production', 'timestamp');
SELECT create_hypertable('weather_data', 'timestamp');
SELECT create_hypertable('ml_features', 'timestamp');

-- Automated data retention policies
SELECT add_retention_policy('solar_production', INTERVAL '2 years');
SELECT add_retention_policy('weather_data', INTERVAL '1 year');
```

### Data Collection Pipeline

#### Automated Historical Sync (Priority 1)
```python
# services/data_collection_service.py
class DataCollectionService:
    async def sync_historical_data(self, days_back: int = 365):
        """Collect historical data to train ML models"""
        # Strategy: Use Deye API historical endpoints if available
        # Fallback: Collect forward from implementation date
        
    async def scheduled_collection(self):
        """Every 15 minutes: collect current data"""
        # Deye inverter data
        # Open-Meteo weather data
        # Feature engineering for ML
```

---

## 2. Weather API Integration - Open-Meteo

### API Selection Rationale
- **Free Tier**: 300,000 calls/month (sufficient for 15-min intervals)
- **South African Coverage**: Global weather models including GFS, ECMWF
- **Solar Variables**: Direct solar radiation, diffuse radiation, cloud cover
- **No Registration Required**: Immediate implementation possible

### Implementation Details

#### Open-Meteo API Configuration
```python
# config.py
OPEN_METEO_CONFIG = {
    "base_url": "https://api.open-meteo.com/v1/forecast",
    "latitude": -25.749,  # Villieria, Pretoria
    "longitude": 28.231,
    "timezone": "Africa/Johannesburg",
    "daily_variables": [
        "temperature_2m_max",
        "temperature_2m_min", 
        "sunshine_duration",
        "precipitation_sum",
        "wind_speed_10m_max"
    ],
    "hourly_variables": [
        "temperature_2m",
        "relative_humidity_2m",
        "precipitation",
        "cloud_cover",
        "wind_speed_10m",
        "shortwave_radiation",  # Solar irradiance
        "direct_radiation",     # Direct solar radiation
        "diffuse_radiation"     # Diffuse solar radiation
    ]
}
```

#### Weather Service Implementation
```python
# services/weather_service.py
class OpenMeteoService:
    async def get_current_weather(self) -> WeatherData:
        """Fetch current weather conditions"""
        
    async def get_solar_forecast(self, days: int = 7) -> List[SolarForecast]:
        """Get solar irradiance forecast for production prediction"""
        
    async def get_historical_weather(self, start_date: str, end_date: str):
        """Fetch historical weather for ML training"""
        # Use historical weather API for model training
```

### South African Solar Context
Based on research data from Pretoria solar radiation patterns:
- **Peak Solar Hours**: 6:54 AM - 5:32 PM (July winter)
- **Expected Daily Irradiance**: 4,200-4,550 Wh/m² (winter months)
- **Seasonal Variations**: Higher values in spring/summer months
- **Hourly Patterns**: Peak irradiance around 12:00-13:00 (600-700 W/m²)

---

## 3. ML Training Pipeline Redesign

### Current Problems with ML Service
1. **Mock Data Generation**: Using `np.random` instead of learning from real patterns
2. **No Pattern Recognition**: Models trained on synthetic data
3. **Generic Outputs**: Recommendations not specific to system behavior

### New ML Architecture

#### Feature Engineering Service
```python
# services/ml_feature_service.py
class SolarFeatureEngineering:
    def create_training_features(self, timeframe_days: int = 365):
        """Generate ML features from historical data"""
        features = {
            # Time-based features
            'hour_of_day': hour,
            'day_of_year': day_of_year,
            'season': self.get_season(date),
            'is_weekend': is_weekend,
            
            # Weather features  
            'solar_irradiance_wm2': irradiance,
            'temperature_celsius': temp,
            'cloud_cover_percent': clouds,
            'humidity_percent': humidity,
            
            # System features
            'inverter_efficiency': efficiency,
            'system_age_days': age,
            'last_maintenance_days': maintenance,
            
            # Derived features
            'irradiance_vs_production_ratio': ratio,
            'temperature_efficiency_impact': temp_impact,
            'weather_stability_index': weather_stability
        }
```

#### Model Training Strategy
```python
# services/ml_training_service.py
class SolarMLTrainer:
    def __init__(self):
        self.models = {
            'production_forecaster': ensemble.RandomForestRegressor(),
            'efficiency_predictor': ensemble.GradientBoostingRegressor(),
            'maintenance_predictor': svm.OneClassSVM()
        }
    
    async def train_production_model(self):
        """Train 24-hour production forecasting model"""
        # Features: weather forecast + historical patterns
        # Target: expected kWh production
        
    async def train_efficiency_model(self):
        """Train system efficiency predictor"""
        # Features: weather conditions + system state
        # Target: efficiency percentage
        

```

#### Real-Time Inference Engine
```python
# services/ml_inference_service.py
class SolarInferenceEngine:
    async def predict_daily_production(self) -> ProductionForecast:
        """24-hour production forecast using weather data"""
        
    async def analyze_current_efficiency(self) -> EfficiencyAnalysis:
        """Real-time efficiency analysis vs expected performance"""
        

        
    async def generate_insights(self) -> AIInsights:
        """Generate specific, actionable insights for this system"""
```

---

## 4. Context Engineering for AI Coders

### AI Assistant Integration Strategy
To enable AI coding assistants to work effectively with this codebase:

#### Documentation Standards
```python
# Every service method must include:
"""
Purpose: What this function does in the solar monitoring context
Input: Expected data types and sources (Deye API, weather API, DB)
Output: What ML/dashboard components consume this data
Context: How this fits into the overall solar analytics pipeline
Example: Sample input/output for AI understanding
"""
```

#### Code Context Comments
```python
# ML CONTEXT: This service trains models on South African solar data
# WEATHER CONTEXT: Integrates Open-Meteo API for Pretoria location
# DATABASE CONTEXT: Stores time-series data for pattern learning
# API CONTEXT: Provides endpoints for dashboard consumption
```

#### Configuration Context
```python
# config/ai_context.py
AI_CODER_CONTEXT = {
    "project_type": "Solar Analytics Platform",
    "location": "Villieria, Pretoria, South Africa",
    "data_sources": ["Deye Solar Inverter API", "Open-Meteo Weather API"],
    "ml_objectives": ["Production Forecasting", "Efficiency Analysis", "Anomaly Detection"],
    "deployment": "FastAPI + SQLite → TimescaleDB migration path",
    "key_constraints": ["15-minute data intervals", "300k monthly API calls", "Real-time inference"]
}
```

---

## 5. Implementation Roadmap

### Phase 3: Database & Data Collection (Weeks 1-2)
1. **Database Schema Implementation**
   - Create SQLite tables for development
   - Implement data models with Pydantic
   - Add database migration system

2. **Data Collection Service**
   - Build automated Deye API collector
   - Implement Open-Meteo weather integration  
   - Create feature engineering pipeline

3. **Historical Data Sync**
   - Collect minimum 30 days of data for initial training
   - Implement data validation and cleaning
   - Create data export/import utilities

### Phase 4: ML Training & Production (Weeks 3-4)
1. **ML Pipeline Development**
   - Replace mock models with real training pipeline
   - Implement cross-validation and model evaluation
   - Add model versioning and experiment tracking

2. **Real-Time Inference**
   - Build production inference API
   - Implement caching and performance optimization
   - Add monitoring and alerting for model performance

3. **Dashboard Integration** 
   - Update frontend to display real ML insights
   - Add forecasting charts and efficiency trends

### Phase 5: Production Deployment (Week 5)
1. **TimescaleDB Migration** (Optional)
   - Migrate from SQLite to TimescaleDB for production
   - Implement automated backups and monitoring
   - Optimize queries for time-series operations

2. **Monitoring & Maintenance**
   - Set up application monitoring
   - Implement automated model retraining
   - Add performance analytics and logging

---

## 6. Technical Specifications

### API Rate Limits & Costs
- **Open-Meteo Free Tier**: 300,000 calls/month
- **Required Calls**: ~3,000/month (15-min intervals)
- **Safety Margin**: 100x over current needs
- **Commercial Backup**: $25/month for 1M calls if needed

### Performance Requirements
- **Database Queries**: < 100ms for dashboard updates
- **ML Inference**: < 500ms for real-time predictions  
- **Weather API**: 15-minute data refresh cycles
- **Data Retention**: 2 years solar data, 1 year weather data

### Accuracy Targets
- **Production Forecasting**: ±15% accuracy for 24-hour predictions
- **Efficiency Analysis**: ±5% accuracy for current performance
- **Anomaly Detection**: <2% false positive rate
- **Weather Correlation**: >0.8 correlation with actual conditions

---

## 7. Research Findings Summary

### Open-Meteo API Capabilities
- **Global Coverage**: Excellent South African coverage with multiple weather models
- **Solar Variables**: Direct radiation, diffuse radiation, cloud cover, sunshine duration
- **Historical Data**: Available for ML training (historical weather API)
- **Real-Time Updates**: Hourly forecasts up to 16 days ahead

### South African Solar Context
- **Pretoria Solar Patterns**: 4.2-4.6 kWh/m²/day typical irradiance
- **Seasonal Variations**: Higher summer production, consistent winter patterns
- **Research Validation**: SAURAN stations provide ground truth for model validation
- **Geographic Factors**: Clear sky models available specifically for South Africa

### ML Training Strategy Validation
- **Minimum Data Requirements**: 30 days minimum, 90 days optimal for initial training
- **Feature Importance**: Solar irradiance > temperature > cloud cover > humidity
- **Model Selection**: Ensemble methods proven effective for solar prediction
- **Anomaly Detection**: Isolation Forest recommended for solar system monitoring

---

## 8. Risk Assessment & Mitigation

### Technical Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Open-Meteo API Rate Limits | High | Low | Monitor usage, implement caching, commercial backup |
| Insufficient Historical Data | High | Medium | Implement forward collection, use synthetic data augmentation |
| Model Accuracy Issues | Medium | Medium | Continuous validation, model ensemble approach |
| Database Performance | Medium | Low | TimescaleDB migration, query optimization |

### Business Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Weather API Changes | Medium | Low | Multiple API provider integration ready |
| Data Quality Issues | High | Medium | Robust validation and cleaning pipelines |
| Maintenance Overhead | Medium | Medium | Automated monitoring and alerting systems |

---

## 9. Success Metrics

### Technical KPIs
- [ ] ML models trained on >30 days real data
- [ ] "NaN%" values eliminated from AI insights
- [ ] <500ms API response times maintained
- [ ] >95% data collection uptime

### Business KPIs  
- [ ] Accurate 24-hour production forecasts
- [ ] System-specific maintenance recommendations
- [ ] Real-time efficiency monitoring
- [ ] Anomaly detection with actionable alerts

### User Experience KPIs
- [ ] Dashboard showing real insights instead of mock data
- [ ] Forecasting charts with confidence intervals
- [ ] Personalized recommendations based on actual system behavior
- [ ] Historical trend analysis with weather correlation

---

## 10. Next Actions

### Immediate (Week 1)
1. **Database Implementation**: Create SQLite schema and models
2. **Weather API Integration**: Set up Open-Meteo service
3. **Data Collection Pipeline**: Begin automated data gathering

### Short-term (Week 2-3)  
1. **ML Pipeline Development**: Replace mock models with real training
2. **Feature Engineering**: Build comprehensive feature extraction
3. **Model Training**: Train initial models on collected data

### Medium-term (Week 4-5)
1. **Production Deployment**: Implement real-time inference
2. **Dashboard Updates**: Display actual ML insights
3. **Performance Optimization**: Implement caching and monitoring

---

**Document Status**: ✅ Complete - Ready for Implementation  
**Research Phase**: ✅ Complete - APIs validated, architecture confirmed  
**Next Step**: Begin Phase 3 database implementation

*This PRD provides the comprehensive roadmap for transforming the Solar Display application from a Phase 1/2 monitoring system into a production-ready AI-powered solar analytics platform with real ML insights, proper data persistence, and South African weather integration.*
