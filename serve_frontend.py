#!/usr/bin/env python3
"""
Simple HTTP server to serve the frontend files
Run this script to serve the frontend on http://localhost:3000
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

# Get the directory of this script
SCRIPT_DIR = Path(__file__).parent
FRONTEND_DIR = SCRIPT_DIR / "frontend"

# Change to frontend directory
os.chdir(FRONTEND_DIR)

PORT = 3000

class CustomHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, X-Access-Code')
        super().end_headers()

def main():
    print(f"Starting frontend server...")
    print(f"Frontend directory: {FRONTEND_DIR.absolute()}")
    print(f"Server will be available at: http://localhost:{PORT}")
    print("Press Ctrl+C to stop the server")
    
    if not FRONTEND_DIR.exists():
        print(f"Error: Frontend directory not found: {FRONTEND_DIR}")
        sys.exit(1)
    
    with socketserver.TCPServer(("", PORT), CustomHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()
