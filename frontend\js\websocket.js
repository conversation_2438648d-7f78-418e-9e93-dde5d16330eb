// WebSocket Client for Real-time Data

class SolarWebSocket {
    constructor() {
        this.ws = null;
        this.stationId = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 5000; // 5 seconds
        this.baseURL = 'ws://localhost:8000/ws';
        this.onDataCallback = null;
        this.onStatusCallback = null;
    }

    // Set callbacks for data and status updates
    setCallbacks(onData, onStatus) {
        this.onDataCallback = onData;
        this.onStatusCallback = onStatus;
    }

    // Connect to WebSocket for a specific station
    connect(stationId) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.disconnect();
        }

        this.stationId = stationId;
        const wsURL = `${this.baseURL}/solar/${stationId}`;
        
        console.log('Connecting to WebSocket:', wsURL);

        try {
            this.ws = new WebSocket(wsURL);
            this.setupEventListeners();
        } catch (error) {
            console.error('WebSocket connection error:', error);
            this.handleConnectionError();
        }
    }

    // Set up WebSocket event listeners
    setupEventListeners() {
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateConnectionStatus('connected', 'Real-time connection active');
            
            // Send ping to keep connection alive
            this.sendPing();
        };

        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            this.isConnected = false;
            this.updateConnectionStatus('disconnected', 'Connection lost');
            
            // Attempt to reconnect if not closed intentionally
            if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            }
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.handleConnectionError();
        };
    }

    // Handle incoming WebSocket messages
    handleMessage(data) {
        switch (data.type) {
            case 'real_time_update':
                if (this.onDataCallback) {
                    this.onDataCallback(data.data);
                }
                break;
            case 'error':
                console.error('WebSocket server error:', data.message);
                this.updateConnectionStatus('error', `Server error: ${data.message}`);
                break;
            case 'pong':
                // Handle ping response
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }

    // Send ping message to keep connection alive
    sendPing() {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({ type: 'ping' }));
            
            // Schedule next ping
            setTimeout(() => this.sendPing(), 30000); // Every 30 seconds
        }
    }

    // Update connection status
    updateConnectionStatus(status, message) {
        if (this.onStatusCallback) {
            this.onStatusCallback(status, message);
        }
    }

    // Handle connection errors
    handleConnectionError() {
        this.isConnected = false;
        this.updateConnectionStatus('error', 'Connection error');
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        } else {
            this.updateConnectionStatus('failed', 'Connection failed - using API polling');
        }
    }

    // Schedule reconnection attempt
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
        
        console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        this.updateConnectionStatus('reconnecting', `Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (this.stationId) {
                this.connect(this.stationId);
            }
        }, delay);
    }

    // Disconnect WebSocket
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, 'User disconnected');
            this.ws = null;
        }
        this.isConnected = false;
        this.stationId = null;
        this.reconnectAttempts = 0;
    }

    // Check if WebSocket is connected
    isWebSocketConnected() {
        return this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN;
    }
}

// Create global WebSocket instance
window.solarWebSocket = new SolarWebSocket();
