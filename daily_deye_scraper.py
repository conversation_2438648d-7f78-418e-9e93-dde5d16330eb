import requests
import hashlib
import csv
from datetime import datetime, timedelta, timezone

# --- Configuration ---
APP_ID = "***************"  # Replace with your App ID
APP_SECRET = "ed106ac532cee8af0fabe6a6e7a27ecd"  # Replace with your App Secret
EMAIL = "<EMAIL>"  # Replace with your Deye Cloud email
PASSWORD = "Dr1jfh0ut@"  # Replace with your Deye Cloud password
CSV_FILE = "deye_daily_data.csv"


def get_token(app_id, app_secret, email, password):
    """Retrieves an access token from the Deye API."""
    url = f"https://eu1-developer.deyecloud.com/v1.0/account/token?appId={app_id}"
    headers = {"Content-Type": "application/json"}

    hashed_password = hashlib.sha256(password.encode('utf-8')).hexdigest().lower()

    data = {
        "appSecret": app_secret,
        "email": email,
        "password": hashed_password,
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        json_response = response.json()

        if json_response.get("success"):
            return json_response.get("accessToken")
        else:
            print(f"Token retrieval failed: {json_response.get('msg')}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"An error occurred during token retrieval: {e}")
        return None

def get_station_list(token):
    """Retrieves the list of stations associated with the account."""
    url = "https://eu1-developer.deyecloud.com/v1.0/station/list"
    headers = {
        "Content-Type": "application/json",
        "authorization": f"Bearer {token}",
    }
    data = {"page": 1, "size": 200} #Get up to 200 stations - adjust if necessary.

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        json_response = response.json()

        if json_response.get("success"):
            return json_response.get("stationList")
        else:
            print(f"Station list retrieval failed: {json_response.get('msg')}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"An error occurred during station list retrieval: {e}")
        return None

def get_station_daily_data(token, station_id, date_str):
    """Retrieves daily historical data for a specific station."""
    url = "https://eu1-developer.deyecloud.com/v1.0/station/history"
    headers = {
        "Content-Type": "application/json",
        "authorization": f"Bearer {token}",
    }
    data = {
        "stationId": station_id,
        "granularity": 2,  # Daily granularity
        "startAt": date_str,
        "endAt": date_str,  # For daily, startAt and endAt are the same
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        json_response = response.json()
        # print(json_response) #for debugging

        if json_response.get("success"):
             #The API returns a list, and we expect only one item for daily data
            if json_response.get("stationDataItems") and len(json_response["stationDataItems"]) > 0 :
                return json_response["stationDataItems"][0] #return the first (and only) day.
            else:
                print(f"No data returned for {date_str}")
                return None
        else:
            print(f"Daily data retrieval failed: {json_response.get('msg')}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"An error occurred during daily data retrieval: {e}")
        return None



def write_to_csv(data, filename):
    """Writes the data to a CSV file."""
    if data is None:
        print("No data to write to CSV.")
        return

    # Prepare data for writing (handle None and format as needed)
    data_to_write = {
        "Date": data.get("year", "N/A") if data.get("year") is not None else "N/A", #Date needs special handling
        "generationValue": data.get("generationValue", "N/A"),
        "consumptionValue": data.get("consumptionValue", "N/A"),
        "gridValue": data.get("gridValue", "N/A"),
        "purchaseValue": data.get("purchaseValue", "N/A"),
        "chargeValue": data.get("chargeValue", "N/A"),
        "dischargeValue": data.get("dischargeValue", "N/A"),
    }
    #Check if we have year, month, day, and form a date
    if "year" in data and data["year"] is not None and "month" in data and data["month"] is not None and "day" in data and data["day"] is not None:
        try:
            #add leading zeros if necessary.
            month_str = str(data["month"] + 1).zfill(2)  # Months are 0-indexed in response, add 1
            day_str = str(data["day"]).zfill(2)
            data_to_write["Date"] = f"{data['year']}-{month_str}-{day_str}"
        except ValueError:
            print(f"Could not create Date from: year:{data.get('year')}, month: {data.get('month')}, day: {data.get('day')}")
            #leave the default 'N/A'

    file_exists = True
    try:
        with open(filename, 'r') as f:
            pass
    except FileNotFoundError:
        file_exists = False

    with open(filename, 'a', newline='') as csvfile:
        fieldnames = list(data_to_write.keys())
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        if not file_exists:
            writer.writeheader()  # Write header only if file doesn't exist

        writer.writerow(data_to_write)
    print(f"Appended data to {filename}")



def main():
    """Main function to orchestrate the data retrieval and storage."""
    token = get_token(APP_ID, APP_SECRET, EMAIL, PASSWORD)
    if not token:
        print("Could not retrieve token. Exiting.")
        return

    stations = get_station_list(token)
    if not stations:
        print("Could not retrieve station list. Exiting.")
        return

    # Get yesterday's date in 'yyyy-MM-dd' format. Use UTC to ensure consistent daily boundary
    yesterday = datetime.now(timezone.utc) - timedelta(days=1)
    yesterday_str = yesterday.strftime("%Y-%m-%d")


    # Example: Get and store data for the first station.  You'll probably want a loop here.
    if len(stations) > 0:
        first_station_id = stations[0].get("id")
        if first_station_id is not None:
          daily_data = get_station_daily_data(token, first_station_id, yesterday_str)

          if daily_data:
              write_to_csv(daily_data, CSV_FILE)
          else:
            print(f"Could not get daily data")
        else:
            print("Could not get first station id")
    else:
        print("No stations found.")

if __name__ == "__main__":
    main()
