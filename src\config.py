from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # API Configuration
    api_title: str = "Solar Monitor API"
    api_version: str = "1.0.0"
    debug: bool = False
    
    # Deye API Settings
    deye_base_url: str = "https://eu1-developer.deyecloud.com/v1.0"
    deye_app_id: str
    deye_app_secret: str
    deye_email: str
    deye_password: str
    
    # Database Configuration
    database_url: Optional[str] = None
    database_path: str = "solar_display.db"
    
    # Data Collection Settings  
    data_collection_interval_minutes: int = 15
    enable_automated_collection: bool = True
    historical_data_retention_days: int = 365
    
    # Weather API Configuration (Open-Meteo)
    weather_api_base_url: str = "https://api.open-meteo.com/v1"
    weather_historical_url: str = "https://archive-api.open-meteo.com/v1"
    pretoria_latitude: float = -25.714331517823403
    pretoria_longitude: float = 28.235238252030513
    timezone: str = "Africa/Johannesburg"
    
    # Redis (for caching)
    redis_url: Optional[str] = "redis://localhost:6379"
    
    # CORS Settings
    cors_origins: str = "http://localhost:3000,http://localhost:8050" # Frontend URLs
    
    # Authentication
    secret_key: str = "a_very_secret_key" # Replace with a real secret key
    access_token_expire_minutes: int = 30
    access_code: str = "1234"  # Simple access code
    
    def is_deye_config_valid(self) -> bool:
        """Check if Deye API configuration contains real values"""
        placeholder_values = ["your_app_id", "your_app_secret", "your_deye_email", "your_deye_password"]
        return not any(
            getattr(self, field) in placeholder_values 
            for field in ["deye_app_id", "deye_app_secret", "deye_email", "deye_password"]
        )
    
    class Config:
        env_file = ".env"

settings = Settings()
