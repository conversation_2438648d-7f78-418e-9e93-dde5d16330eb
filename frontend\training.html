<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ML Model Training - Solar Display</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .training-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .training-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .training-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }
        
        .training-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .training-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .training-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .training-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-container {
            margin: 20px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 25px;
            background: #f0f0f0;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 12px;
            transition: width 0.5s ease;
            position: relative;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 12px;
            z-index: 2;
        }
        
        .training-stages {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stage-item {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .stage-item.completed {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .stage-item.active {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .stage-item.pending {
            border-color: #e0e0e0;
            background: #f9f9f9;
        }
        
        .stage-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .stage-item.completed .stage-icon {
            color: #4CAF50;
        }
        
        .stage-item.active .stage-icon {
            color: #667eea;
        }
        
        .stage-item.pending .stage-icon {
            color: #ccc;
        }
        
        .logs-container {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            border: 2px solid #333;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-timestamp {
            color: #888;
            margin-right: 10px;
        }
        
        .log-level-info {
            color: #00ff00;
        }
        
        .log-level-warning {
            color: #ffaa00;
        }
        
        .log-level-error {
            color: #ff0000;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .metric-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .metric-description {
            color: #666;
            font-size: 14px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ready {
            background: #4CAF50;
        }
        
        .status-training {
            background: #FF9800;
            animation: pulse 1s infinite;
        }
        
        .status-error {
            background: #F44336;
        }
        
        .status-pending {
            background: #9E9E9E;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .days-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .days-input {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            width: 80px;
        }
        
        .refresh-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .refresh-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <h1><i class="fas fa-solar-panel"></i> Solar Display</h1>
            <div class="nav-links">
                <a href="index.html"><i class="fas fa-home"></i> Dashboard</a>
                <a href="training.html" class="active"><i class="fas fa-brain"></i> Training</a>
            </div>
        </div>
    </nav>

    <div class="training-container">
        <div class="training-header">
            <h1><i class="fas fa-brain"></i> ML Model Training Center</h1>
            <p>Train and manage machine learning models for solar energy prediction and analysis</p>
        </div>

        <!-- Training Controls -->
        <div class="training-card">
            <h2><i class="fas fa-cog"></i> Training Controls</h2>
            <div class="training-controls">
                <div class="days-selector">
                    <label for="daysBack">Training Data (days):</label>
                    <input type="number" id="daysBack" class="days-input" value="30" min="7" max="365">
                </div>
                <button id="startTraining" class="training-button">
                    <i class="fas fa-play"></i> Start Training
                </button>
                <button id="refreshStatus" class="refresh-button">
                    <i class="fas fa-sync-alt"></i> Refresh Status
                </button>
            </div>
        </div>

        <!-- Training Progress -->
        <div class="training-card">
            <h2><i class="fas fa-chart-line"></i> Training Progress</h2>
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill" style="width: 0%">
                        <div id="progressText" class="progress-text">0%</div>
                    </div>
                </div>
                <div id="currentTask" class="current-task" style="margin-top: 10px; font-weight: bold;">
                    Ready to start training
                </div>
            </div>
        </div>

        <!-- Training Stages -->
        <div class="training-card">
            <h2><i class="fas fa-tasks"></i> Training Stages</h2>
            <div class="training-stages">
                <div class="stage-item pending" id="stage-data-collection">
                    <div class="stage-icon"><i class="fas fa-database"></i></div>
                    <h3>Data Collection</h3>
                    <p>Gathering historical solar data from Deye API</p>
                </div>
                <div class="stage-item pending" id="stage-forecaster">
                    <div class="stage-icon"><i class="fas fa-chart-line"></i></div>
                    <h3>Production Forecaster</h3>
                    <p>Training solar generation prediction model</p>
                </div>
                <div class="stage-item pending" id="stage-anomaly">
                    <div class="stage-icon"><i class="fas fa-exclamation-triangle"></i></div>
                    <h3>Anomaly Detection</h3>
                    <p>Training system health monitoring models</p>
                </div>
                <div class="stage-item pending" id="stage-consumption">
                    <div class="stage-icon"><i class="fas fa-chart-pie"></i></div>
                    <h3>Consumption Analysis</h3>
                    <p>Analyzing energy usage patterns</p>
                </div>
                <div class="stage-item pending" id="stage-maintenance">
                    <div class="stage-icon"><i class="fas fa-tools"></i></div>
                    <h3>Maintenance Prediction</h3>
                    <p>Training predictive maintenance model</p>
                </div>
                <div class="stage-item pending" id="stage-saving">
                    <div class="stage-icon"><i class="fas fa-save"></i></div>
                    <h3>Model Saving</h3>
                    <p>Saving trained models to disk</p>
                </div>
            </div>
        </div>

        <!-- Training Logs -->
        <div class="training-card">
            <h2><i class="fas fa-terminal"></i> Training Logs</h2>
            <div id="logsContainer" class="logs-container">
                <div class="log-entry">
                    <span class="log-timestamp">[READY]</span>
                    <span class="log-level-info">Training system initialized. Click 'Start Training' to begin.</span>
                </div>
            </div>
        </div>

        <!-- Model Status -->
        <div class="training-card">
            <h2><i class="fas fa-info-circle"></i> Model Status</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-title">
                        <span id="forecasterStatus" class="status-indicator status-pending"></span>
                        Production Forecaster
                    </div>
                    <div id="forecasterMetric" class="metric-value">Not Trained</div>
                    <div class="metric-description">Solar generation prediction model</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">
                        <span id="anomalyStatus" class="status-indicator status-pending"></span>
                        Anomaly Detector
                    </div>
                    <div id="anomalyMetric" class="metric-value">Not Trained</div>
                    <div class="metric-description">System health monitoring</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">
                        <span id="consumptionStatus" class="status-indicator status-pending"></span>
                        Consumption Analyzer
                    </div>
                    <div id="consumptionMetric" class="metric-value">Not Trained</div>
                    <div class="metric-description">Energy usage pattern analysis</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">
                        <span id="maintenanceStatus" class="status-indicator status-pending"></span>
                        Maintenance Predictor
                    </div>
                    <div id="maintenanceMetric" class="metric-value">Not Trained</div>
                    <div class="metric-description">Predictive maintenance model</div>
                </div>
            </div>
        </div>

        <!-- Training Metrics -->
        <div class="training-card" id="trainingMetrics" style="display: none;">
            <h2><i class="fas fa-chart-bar"></i> Training Metrics</h2>
            <div class="metrics-grid" id="metricsGrid">
                <!-- Metrics will be populated here during training -->
            </div>
        </div>
    </div>

    <script src="js/training.js"></script>
</body>
</html>
