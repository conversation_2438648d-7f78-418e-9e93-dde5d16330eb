#!/usr/bin/env python3
"""
Startup script for Solar Display Application - Phase 3
Starts both the FastAPI backend and frontend server with enhanced features
"""

import subprocess
import sys
import time
import signal
import os
from pathlib import Path

def start_backend():
    """Start the FastAPI backend server"""
    print("Starting FastAPI backend server...")
    backend_process = subprocess.Popen([
        sys.executable, "-m", "uvicorn", 
        "src.main:app", 
        "--host", "0.0.0.0", 
        "--port", "8000",
        "--reload"
    ])
    return backend_process

def start_frontend():
    """Start the frontend server"""
    print("Starting frontend server...")
    
    # Get the frontend directory
    frontend_dir = Path(__file__).parent / "frontend"
    print(f"Frontend directory: {frontend_dir}")
    
    # Start simple HTTP server for frontend
    frontend_process = subprocess.Popen([
        sys.executable, "-m", "http.server", "3000"
    ], cwd=frontend_dir)
    
    print("Server will be available at: http://localhost:3000")
    print("Press Ctrl+C to stop the server")
    
    return frontend_process

def signal_handler(sig, frame):
    """Handle shutdown signals"""
    print("\n🛑 Shutting down servers...")
    # Terminate all child processes
    for proc in processes:
        if proc.poll() is None:  # Process is still running
            proc.terminate()
    
    # Wait a bit for graceful shutdown
    time.sleep(2)
    
    # Force kill if still running
    for proc in processes:
        if proc.poll() is None:
            proc.kill()
    
    print("✅ All servers stopped successfully!")
    sys.exit(0)

def main():
    """Main startup function"""
    global processes
    processes = []
    
    print("============================================================")
    print("Solar Display Application Startup")
    print("============================================================")
    
    try:
        # Start backend
        backend_proc = start_backend()
        processes.append(backend_proc)
        
        # Wait a moment for backend to start
        time.sleep(3)
        
        # Start frontend
        frontend_proc = start_frontend()
        processes.append(frontend_proc)
        
        # Wait another moment for frontend to start
        time.sleep(2)
        
        print("\n============================================================")
        print("🚀 Solar Display Application Started Successfully!")
        print("============================================================")
        print("📊 Frontend Dashboard: http://localhost:3000")
        print("🔧 Backend API Docs:   http://localhost:8000/api/docs")
        print("� Backend API:        http://localhost:8000/api")
        print("")
        print("📝 Default access code: 1234")
        print("   (Change in .env file: ACCESS_CODE=your_code)")
        print("")
        print("🛑 Press Ctrl+C to stop all servers")
        print("============================================================")
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Keep the main process alive
        while True:
            # Check if any process has died
            for i, proc in enumerate(processes):
                if proc.poll() is not None:
                    print(f"Process {i} has stopped. Restarting...")
                    if i == 0:  # Backend
                        processes[i] = start_backend()
                    else:  # Frontend
                        processes[i] = start_frontend()
            
            time.sleep(5)  # Check every 5 seconds
            
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        signal_handler(signal.SIGTERM, None)

if __name__ == "__main__":
    main()
