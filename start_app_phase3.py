"""
Enhanced startup script for Solar Display Application - Phase 3

Initializes the complete solar analytics platform with:
- Database infrastructure
- Weather API integration 
- Automated data collection
- ML feature engineering pipeline

This startup script transforms the application from monitoring-only
to a production-ready AI-powered solar analytics platform.
"""

import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.database import initialize_database, get_database_stats
from src.services.data_collection_service import get_data_collection_service
from src.services.weather_service import get_weather_service
from src.config import settings

# Configure logging
logging.basicConfig(
 level=logging.INFO,
 format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
 handlers=[
 logging.FileHandler('solar_display.log'),
 logging.StreamHandler(sys.stdout)
 ]
)

logger = logging.getLogger(__name__)

async def initialize_phase3_infrastructure():
 """
 Initialize Phase 3 infrastructure for data-driven solar analytics
 
 Sets up:
 - SQLite database with time-series optimized schema
 - Weather API service connection
 - Data collection pipeline
 - ML feature engineering
 """
 logger.info("Initializing Solar Display Phase 3 - Database & Weather Integration")
 logger.info(f"Location: Villieria, Pretoria, South Africa ({settings.pretoria_latitude}°S, {settings.pretoria_longitude}°E)")
 
 try:
 # Step 1: Initialize database infrastructure
 logger.info("Initializing database infrastructure...")
 await initialize_database()
 
 # Get database statistics
 db_stats = await get_database_stats()
 logger.info(f"Database Status: {db_stats}")
 
 # Step 2: Initialize weather service
 logger.info("Initializing weather service...")
 weather_service = await get_weather_service()
 
 # Test weather API connection
 try:
 current_weather = await weather_service.get_current_weather()
 logger.info(f"Current weather: {current_weather.temperature}°C, {current_weather.cloud_cover}% clouds, {current_weather.solar_radiation}W/m²")
 except Exception as e:
 logger.warning(f"Weather API test failed: {e} - Will continue with estimates")
 
 # Step 3: Initialize data collection service
 logger.info("Initializing data collection service...")
 data_collection_service = await get_data_collection_service()
 await data_collection_service.initialize()
 
 # Step 4: Sync some historical weather data if database is empty
 if db_stats.get('weather_data_count', 0) == 0:
 logger.info("Syncing historical weather data for ML training...")
 try:
 await data_collection_service.sync_historical_data(days_back=30)
 logger.info("✅ Historical weather data sync completed")
 except Exception as e:
 logger.warning(f"Historical sync failed: {e} - Will build data going forward")
 
 # Step 5: Start automated data collection if enabled
 if settings.enable_automated_collection:
 logger.info(f"🔄 Starting automated data collection (every {settings.data_collection_interval_minutes} minutes)")
 
 # Start data collection in background
 data_collection_task = asyncio.create_task(
 data_collection_service.start_automated_collection()
 )
 
 # Give it a moment to start
 await asyncio.sleep(2)
 
 # Check if it started successfully
 collection_stats = data_collection_service.get_collection_stats()
 logger.info(f"📊 Data collection status: {collection_stats}")
 
 return data_collection_task
 else:
 logger.info("⏸️ Automated data collection is disabled in configuration")
 return None
 
 except Exception as e:
 logger.error(f"❌ Failed to initialize Phase 3 infrastructure: {e}")
 raise

async def run_data_collection_demo():
 """
 Run a single data collection cycle for demonstration
 """
 logger.info("🧪 Running data collection demonstration...")
 
 try:
 data_collection_service = await get_data_collection_service()
 await data_collection_service.initialize()
 
 # Perform one collection cycle
 await data_collection_service.collect_and_store_data()
 
 # Show statistics
 stats = data_collection_service.get_collection_stats()
 logger.info(f"📊 Demo collection completed: {stats}")
 
 # Show database stats
 db_stats = await get_database_stats()
 logger.info(f"🗄️ Database after demo: {db_stats}")
 
 except Exception as e:
 logger.error(f"❌ Demo collection failed: {e}")

async def main():
 """Main startup function"""
 start_time = datetime.now()
 logger.info(f"🚀 Starting Solar Display Application - {start_time}")
 
 # Check Deye API configuration
 if not settings.is_deye_config_valid():
 logger.warning("⚠️ Deye API credentials not configured - some features will use demo data")
 logger.info("📝 Please update your .env file with real Deye Cloud credentials for full functionality")
 
 try:
 # Initialize Phase 3 infrastructure
 data_collection_task = await initialize_phase3_infrastructure()
 
 # Calculate initialization time
 init_time = (datetime.now() - start_time).total_seconds()
 logger.info(f"⚡ Phase 3 initialization completed in {init_time:.2f} seconds")
 
 # Import and start the main application
 logger.info("🖥️ Starting FastAPI application...")
 
 # Import the main app
 from src.main import app
 
 # If we have automated collection running, we need to handle it properly
 if data_collection_task:
 logger.info("🔄 Automated data collection is running in background")
 logger.info("📊 Check logs for collection status every 15 minutes")
 
 logger.info("🌐 Solar Display API server starting on http://localhost:8000")
 logger.info("📚 API Documentation available at http://localhost:8000/api/docs")
 logger.info("🎛️ Dashboard available via frontend (run serve_frontend.py)")
 
 # Return the data collection task to run alongside the server
 return data_collection_task
 
 except KeyboardInterrupt:
 logger.info("🛑 Shutdown requested by user")
 except Exception as e:
 logger.error(f"❌ Application startup failed: {e}")
 raise
 finally:
 # Cleanup
 logger.info("🧹 Cleaning up resources...")
 
 # Stop data collection if running
 try:
 data_collection_service = await get_data_collection_service()
 data_collection_service.stop_automated_collection()
 except:
 pass
 
 # Close weather service
 try:
 weather_service = await get_weather_service()
 await weather_service.close_session()
 except:
 pass
 
 logger.info("Solar Display Application shutdown complete")


def run_server():
 """Run the server with proper event loop handling"""
 import uvicorn
 
 # Run initialization in a separate event loop
 loop = asyncio.new_event_loop()
 asyncio.set_event_loop(loop)
 
 try:
 # Initialize the Phase 3 infrastructure
 data_collection_task = loop.run_until_complete(main())
 
 # Import the main app after initialization
 from src.main import app
 
 # Start the FastAPI server
 uvicorn.run(
 app,
 host="0.0.0.0",
 port=8000,
 log_level="info",
 access_log=True
 )
 finally:
 loop.close()


if __name__ == "__main__":
 # Check if demo mode was requested
 if len(sys.argv) > 1 and sys.argv[1] == "--demo":
 asyncio.run(run_data_collection_demo())
 else:
 run_server()
