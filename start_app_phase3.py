#!/usr/bin/env python3
"""
Startup script for Solar Display Application - Phase 3
Starts both the FastAPI backend and frontend server with enhanced features
"""

import subprocess
import sys
import time
import signal
import os
import socket
from pathlib import Path

def check_port_available(port, host='127.0.0.1'):
    """Check if a port is available"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex((host, port))
            return result != 0  # Port is available if connection fails
    except Exception:
        return False

def find_available_port(start_port=3000, max_attempts=10):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        if check_port_available(port):
            return port
    return None

def start_backend():
    """Start the FastAPI backend server"""
    print("Starting FastAPI backend server...")
    backend_process = subprocess.Popen([
        sys.executable, "-m", "uvicorn", 
        "src.main:app", 
        "--host", "0.0.0.0", 
        "--port", "8000",
        "--reload"
    ])
    return backend_process

def start_frontend():
    """Start the frontend server"""
    print("Starting frontend server...")

    # Get the frontend directory
    frontend_dir = Path(__file__).parent / "frontend"
    print(f"Frontend directory: {frontend_dir}")

    # Check if frontend directory exists
    if not frontend_dir.exists():
        print(f"❌ Frontend directory not found: {frontend_dir}")
        return None, None

    # Check if index.html exists
    index_file = frontend_dir / "index.html"
    if not index_file.exists():
        print(f"❌ index.html not found: {index_file}")
        return None, None

    # Check if port 3000 is available
    if not check_port_available(3000):
        print("⚠️  Port 3000 is already in use, finding alternative...")
        available_port = find_available_port(3001)
        if available_port:
            print(f"Using port {available_port} instead")
            port = available_port
        else:
            print("❌ No available ports found")
            return None, None
    else:
        port = 3000

    # Try to start the HTTP server with better error handling
    try:
        # Use Python's http.server with explicit binding
        frontend_process = subprocess.Popen([
            sys.executable, "-m", "http.server", str(port),
            "--bind", "127.0.0.1"
        ], cwd=frontend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # Give it a moment to start
        time.sleep(2)

        # Check if the process is still running
        if frontend_process.poll() is None:
            print("✅ Frontend server started successfully")
            print(f"Server will be available at: http://localhost:{port}")
        else:
            # Process died, get the error
            stdout, stderr = frontend_process.communicate()
            print(f"❌ Frontend server failed to start:")
            if stderr:
                print(f"Error: {stderr.decode()}")
            if stdout:
                print(f"Output: {stdout.decode()}")
            return None, None

    except Exception as e:
        print(f"❌ Failed to start frontend server: {e}")
        return None, None

    return frontend_process, port

def signal_handler(sig, frame):
    """Handle shutdown signals"""
    print("\n🛑 Shutting down servers...")
    # Terminate all child processes
    for proc in processes:
        if proc.poll() is None:  # Process is still running
            proc.terminate()
    
    # Wait a bit for graceful shutdown
    time.sleep(2)
    
    # Force kill if still running
    for proc in processes:
        if proc.poll() is None:
            proc.kill()
    
    print("✅ All servers stopped successfully!")
    sys.exit(0)

def main():
    """Main startup function"""
    global processes
    processes = []
    
    print("============================================================")
    print("Solar Display Application Startup")
    print("============================================================")
    
    try:
        # Start backend
        backend_proc = start_backend()
        processes.append(backend_proc)
        
        # Wait a moment for backend to start
        time.sleep(3)
        
        # Start frontend
        frontend_result = start_frontend()
        if frontend_result[0] is None:
            print("❌ Failed to start frontend server")
            signal_handler(signal.SIGTERM, None)
            return

        frontend_proc, frontend_port = frontend_result
        processes.append(frontend_proc)

        # Wait another moment for frontend to start
        time.sleep(2)

        print("\n============================================================")
        print("🚀 Solar Display Application Started Successfully!")
        print("============================================================")
        print(f"📊 Frontend Dashboard: http://localhost:{frontend_port}")
        print("🔧 Backend API Docs:   http://localhost:8000/api/docs")
        print("🔌 Backend API:        http://localhost:8000/api")
        print("")
        print("📝 Default access code: 1234")
        print("   (Change in .env file: ACCESS_CODE=your_code)")
        print("")
        print("🛑 Press Ctrl+C to stop all servers")
        print("============================================================")
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Keep the main process alive
        while True:
            # Check if any process has died
            for i, proc in enumerate(processes):
                if proc.poll() is not None:
                    print(f"Process {i} has stopped. Restarting...")
                    if i == 0:  # Backend
                        processes[i] = start_backend()
                    else:  # Frontend
                        processes[i] = start_frontend()
            
            time.sleep(5)  # Check every 5 seconds
            
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        signal_handler(signal.SIGTERM, None)

if __name__ == "__main__":
    main()
