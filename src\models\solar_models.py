from pydantic import BaseModel, Field
from datetime import datetime
from typing import List, Optional

class SolarStation(BaseModel):
    id: str = Field(..., description="Unique station identifier")
    name: str = Field(..., description="Station display name")
    capacity: float = Field(..., description="Total capacity in kW")
    location: str = Field(..., description="Geographic location")
    status: str = Field(..., description="Current status")
    last_update: datetime = Field(..., description="Last data update")

class RealTimeData(BaseModel):
    station_id: str
    timestamp: datetime
    generation_power: float = Field(..., description="Current solar generation (kW)")
    battery_soc: float = Field(..., description="Battery state of charge (%)")
    battery_power: float = Field(..., description="Battery charge/discharge (kW)")
    grid_power: float = Field(..., description="Grid import/export (kW)")
    consumption_power: float = Field(..., description="Current consumption (kW)")
    irradiate_intensity: Optional[float] = Field(None, description="Solar irradiation")

class HistoricalDataPoint(BaseModel):
    date: str
    generation_value: float = Field(..., description="Daily generation (kWh)")
    consumption_value: float = Field(..., description="Daily consumption (kWh)")
    grid_value: float = Field(..., description="Net grid exchange (kWh)")
    battery_charge: float = Field(..., description="Battery charged (kWh)")
    battery_discharge: float = Field(..., description="Battery discharged (kWh)")

class HistoricalData(BaseModel):
    station_id: str
    data: List[HistoricalDataPoint]

class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class StationListResponse(APIResponse):
    data: List[SolarStation]

class RealTimeResponse(APIResponse):
    data: RealTimeData

class HistoricalResponse(APIResponse):
    data: HistoricalData
