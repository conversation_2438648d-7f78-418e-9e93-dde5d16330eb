from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from src.models.auth_models import Token, AccessCode
from src.services.auth_service import verify_access_code

router = APIRouter()

@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: AccessCode):
    """
    Provides a token if the access code is valid.
    This is a simplified login flow.
    """
    if not verify_access_code(form_data.code):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect access code",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # In a real OAuth2 flow, you would create a JWT token here.
    # For simplicity, we'll return the access code itself as the token.
    # The frontend will then send this in the `X-Access-Code` header.
    access_token = form_data.code
    return {"access_token": access_token, "token_type": "bearer"}
