#!/usr/bin/env python3
"""
Manual Frontend Starter for Solar Display Application
Use this if the main startup script has issues
"""

import subprocess
import sys
import time
import signal
import socket
from pathlib import Path

def check_port_available(port, host='127.0.0.1'):
    """Check if a port is available"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex((host, port))
            return result != 0
    except Exception:
        return False

def find_available_port(start_port=3000, max_attempts=10):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        if check_port_available(port):
            return port
    return None

def main():
    """Start just the frontend server"""
    print("============================================================")
    print("Solar Display Frontend Server")
    print("============================================================")
    
    # Get the frontend directory
    frontend_dir = Path(__file__).parent / "frontend"
    print(f"Frontend directory: {frontend_dir}")
    
    # Check if frontend directory exists
    if not frontend_dir.exists():
        print(f"❌ Frontend directory not found: {frontend_dir}")
        return
    
    # Check if index.html exists
    index_file = frontend_dir / "index.html"
    if not index_file.exists():
        print(f"❌ index.html not found: {index_file}")
        return
    
    # Find available port
    if not check_port_available(3000):
        print("⚠️  Port 3000 is already in use, finding alternative...")
        port = find_available_port(3001)
        if not port:
            print("❌ No available ports found")
            return
        print(f"Using port {port} instead")
    else:
        port = 3000
    
    print(f"Starting frontend server on port {port}...")
    
    try:
        # Start the HTTP server
        process = subprocess.Popen([
            sys.executable, "-m", "http.server", str(port), 
            "--bind", "127.0.0.1"
        ], cwd=frontend_dir)
        
        print("✅ Frontend server started successfully!")
        print(f"📊 Frontend Dashboard: http://localhost:{port}")
        print("🔧 Make sure backend is running on: http://localhost:8000")
        print("")
        print("📝 Default access code: 1234")
        print("🛑 Press Ctrl+C to stop the server")
        print("============================================================")
        
        # Handle shutdown
        def signal_handler(sig, frame):
            print("\n🛑 Shutting down frontend server...")
            process.terminate()
            process.wait()
            print("✅ Frontend server stopped!")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Keep running
        process.wait()
        
    except Exception as e:
        print(f"❌ Failed to start frontend server: {e}")
        print("\nTry running manually:")
        print(f"cd {frontend_dir}")
        print(f"python -m http.server {port}")

if __name__ == "__main__":
    main()
