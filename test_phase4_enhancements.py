#!/usr/bin/env python3
"""
Phase 4 ML Enhancements Testing Script

Tests the enhanced AI capabilities of the Solar Display application
including weather-based forecasting, real-time performance analysis,
and ML analytics capabilities.
"""

import asyncio
import httpx
import json
from datetime import datetime
from typing import Dict, Any

# API Configuration
BASE_URL = "http://localhost:8000/api"
ACCESS_CODE = "1234"
STATION_ID = "demo_station"

class Phase4Tester:
    """Test suite for Phase 4 ML enhancements"""
    
    def __init__(self):
        self.headers = {"X-Access-Code": ACCESS_CODE}
        self.passed_tests = 0
        self.total_tests = 0
        
    async def test_endpoint(self, endpoint: str, test_name: str) -> Dict[str, Any]:
        """Test a specific API endpoint"""
        self.total_tests += 1
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{BASE_URL}{endpoint}", headers=self.headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ {test_name}: SUCCESS")
                    self.passed_tests += 1
                    return data
                else:
                    print(f"❌ {test_name}: FAILED (HTTP {response.status_code})")
                    print(f"   Error: {response.text}")
                    return None
                    
        except Exception as e:
            print(f"❌ {test_name}: FAILED (Exception: {e})")
            return None
    
    def analyze_forecast_data(self, data: Dict[str, Any]) -> None:
        """Analyze forecast data quality"""
        if not data:
            return
            
        summary = data.get('summary', {})
        forecast_data = data.get('forecast_data', [])
        
        print(f"   📊 Total Predicted Generation: {summary.get('total_predicted_generation', 0)} kWh")
        print(f"   🔋 Peak Generation Time: {summary.get('peak_generation_time', 'N/A')}")
        print(f"   📈 Peak Power: {summary.get('peak_generation_power', 0)} kW")
        print(f"   🎯 Average Confidence: {summary.get('average_confidence', 0)*100:.1f}%")
        print(f"   📋 Data Points: {len(forecast_data)}")
        
        # Check for realistic values
        peak_power = summary.get('peak_generation_power', 0)
        if peak_power > 0 and peak_power < 20:  # Reasonable for residential solar
            print(f"   ✅ Peak power looks realistic ({peak_power} kW)")
        else:
            print(f"   ⚠️  Peak power might be unrealistic ({peak_power} kW)")
    
    def analyze_performance_data(self, data: Dict[str, Any]) -> None:
        """Analyze performance metrics"""
        if not data:
            return
            
        analysis = data.get('performance_analysis', {})
        metrics = analysis.get('metrics', {})
        
        print(f"   ⚡ System Efficiency: {metrics.get('system_efficiency', 0)*100:.1f}%")
        print(f"   🔋 Battery Health: {metrics.get('battery_health_score', 0)*100:.1f}%")
        print(f"   🏠 Self-Sufficiency: {metrics.get('self_sufficiency_ratio', 0)*100:.1f}%")
        print(f"   📈 Generation Trend: {metrics.get('generation_trend', 'unknown')}")
        
        # Check for reasonable efficiency
        efficiency = metrics.get('system_efficiency', 0)
        if 0.5 <= efficiency <= 1.0:  # 50-100% efficiency is reasonable
            print(f"   ✅ System efficiency looks realistic ({efficiency*100:.1f}%)")
        else:
            print(f"   ⚠️  System efficiency might be unrealistic ({efficiency*100:.1f}%)")

    async def run_comprehensive_test(self):
        """Run comprehensive Phase 4 testing"""
        print("=" * 60)
        print("🚀 Solar Display - Phase 4 ML Enhancements Test Suite")
        print("=" * 60)
        print(f"🕒 Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔗 API Base URL: {BASE_URL}")
        print(f"🎯 Station ID: {STATION_ID}")
        print()
        
        # Test 1: Enhanced Weather-Based Forecasting
        print("1️⃣ Testing Enhanced Weather-Based Forecasting...")
        forecast_data = await self.test_endpoint(
            f"/ml/analytics/forecast/{STATION_ID}?hours=24",
            "Weather-Based 24h Forecast"
        )
        if forecast_data:
            self.analyze_forecast_data(forecast_data)
        print()
        
        # Test 2: Real-Time Performance Analysis
        print("2️⃣ Testing Real-Time Performance Analysis...")
        performance_data = await self.test_endpoint(
            f"/ml/analytics/performance/{STATION_ID}?days_back=7",
            "7-Day Performance Analysis"
        )
        if performance_data:
            self.analyze_performance_data(performance_data)
        print()
        
        # Test 3: Maintenance Recommendations
        print("3️⃣ Testing Maintenance Recommendations...")
        maintenance_data = await self.test_endpoint(
            f"/ml/analytics/maintenance/{STATION_ID}",
            "Predictive Maintenance"
        )
        print()
        
        # Test 4: Consumption Insights
        print("4️⃣ Testing Consumption Insights...")
        consumption_data = await self.test_endpoint(
            f"/ml/analytics/consumption/{STATION_ID}",
            "Consumption Pattern Analysis"
        )
        print()
        
        # Test 5: Comprehensive Daily Insights
        print("5️⃣ Testing Comprehensive Daily Insights...")
        insights_data = await self.test_endpoint(
            f"/ml/analytics/insights/{STATION_ID}?days_back=7",
            "7-Day AI Insights"
        )
        print()
        
        # Final Results
        print("=" * 60)
        print("📊 PHASE 4 TEST RESULTS SUMMARY")
        print("=" * 60)
        print(f"✅ Tests Passed: {self.passed_tests}")
        print(f"❌ Tests Failed: {self.total_tests - self.passed_tests}")
        print(f"📈 Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        if self.passed_tests == self.total_tests:
            print("\n🎉 ALL PHASE 4 ENHANCEMENTS WORKING PERFECTLY!")
            print("   ✅ Weather-based forecasting active")
            print("   ✅ Real-time performance analytics working")
            print("   ✅ ML insights providing realistic values")
        else:
            print(f"\n⚠️  Some issues detected. Please check failed tests above.")
        
        print("\n" + "=" * 60)

async def main():
    """Main test execution"""
    tester = Phase4Tester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
