"""
ML Analytics API Router

Provides endpoints for AI-powered solar energy insights:
- Daily insights with forecasting
- Model training endpoints
- Performance analytics
- Maintenance recommendations
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import pandas as pd
import numpy as np
import logging
import json

from src.services.ml_service import MLAnalyticsService, get_ml_service
from src.services.deye_service import DeyeAPIService
from src.services.historical_data_service import HistoricalDataService
from src.services.auth_service import get_current_user


def clean_json_data(data):
    """Clean data for JSON serialization by handling NaN and infinity values"""
    if isinstance(data, dict):
        return {k: clean_json_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [clean_json_data(item) for item in data]
    elif isinstance(data, (np.float64, np.float32)):
        if np.isnan(data) or np.isinf(data):
            return None
        return float(data)
    elif isinstance(data, (np.int64, np.int32)):
        return int(data)
    elif pd.isna(data):
        return None
    else:
        return data

logger = logging.getLogger(__name__)

router = APIRouter()

async def get_deye_service():
    """Get Deye API service"""
    service = DeyeAPIService()
    try:
        yield service
    finally:
        await service.close_session()

def get_historical_data_service() -> HistoricalDataService:
    """Get Historical Data service"""
    return HistoricalDataService()

@router.get("/analytics/insights/{station_id}")
async def get_station_insights(
    station_id: str,
    days_back: int = Query(default=7, ge=1, le=30, description="Days of historical data to analyze"),
    current_user: dict = Depends(get_current_user),
    ml_service: MLAnalyticsService = Depends(get_ml_service),
    deye_service: DeyeAPIService = Depends(get_deye_service),
    historical_service: HistoricalDataService = Depends(get_historical_data_service)
):
    """Get AI-powered insights for a solar station"""
    try:
        logger.info(f"Generating insights for station {station_id}, {days_back} days back")
        
        # Get historical data from Deye API
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # Get station data
            history_response = await deye_service.get_station_history_data(
                station_id=station_id,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d'),
                granularity=1  # Hourly data
            )
            
            # Convert to pandas DataFrame
            historical_data = _convert_to_dataframe(history_response)
            
        except Exception as e:
            logger.warning(f"Could not fetch real data for station {station_id}: {e}")
            # Generate mock data for demonstration
            historical_data = _generate_mock_data(days_back)
        
        # Generate insights using ML service
        insights = await ml_service.generate_daily_insights(station_id, historical_data)
        
        response_data = {
            'success': True,
            'station_id': station_id,
            'analysis_period': f'{days_back} days',
            'insights': insights,
            'data_points_analyzed': len(historical_data),
            'generated_at': datetime.now().isoformat()
        }
        
        return clean_json_data(response_data)
        
    except Exception as e:
        logger.error(f"Error generating insights for station {station_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate insights: {str(e)}")

@router.post("/analytics/train/{station_id}")
async def train_models(
    station_id: str,
    background_tasks: BackgroundTasks,
    days_back: int = Query(default=30, ge=7, le=90, description="Days of data for training"),
    current_user: dict = Depends(get_current_user),
    ml_service: MLAnalyticsService = Depends(get_ml_service)
):
    """Trigger model training for a station"""
    
    try:
        logger.info(f"Starting model training for station {station_id}")
        
        # Add training task to background
        background_tasks.add_task(
            _train_station_models,
            ml_service,
            station_id,
            days_back
        )
        
        return {
            'success': True,
            'message': 'Model training started in background',
            'station_id': station_id,
            'training_data_period': f'{days_back} days',
            'estimated_completion': '5-10 minutes'
        }
        
    except Exception as e:
        logger.error(f"Error starting model training for station {station_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start training: {str(e)}")

@router.get("/analytics/forecast/{station_id}")
async def get_production_forecast(
    station_id: str,
    hours: int = Query(default=24, ge=1, le=168, description="Hours to forecast"),
    current_user: dict = Depends(get_current_user),
    ml_service: MLAnalyticsService = Depends(get_ml_service)
):
    """Get production forecast for the next N hours"""
    try:
        logger.info(f"Generating {hours}h forecast for station {station_id}")
        
        # Get weather service for forecast enhancement
        from src.services.weather_service import OpenMeteoService
        weather_service = OpenMeteoService()
        
        # Generate forecast with weather data
        forecast = await ml_service.forecaster.predict_daily_generation(station_id, hours, weather_service)
        
        # Calculate summary statistics
        total_predicted = forecast['predicted_generation'].sum()
        peak_hour = forecast.loc[forecast['predicted_generation'].idxmax(), 'timestamp']
        peak_generation = forecast['predicted_generation'].max()
        
        response_data = {
            'success': True,
            'station_id': station_id,
            'forecast_period': f'{hours} hours',
            'forecast_data': forecast.to_dict('records'),
            'summary': {
                'total_predicted_generation': round(total_predicted, 2),
                'peak_generation_time': peak_hour.strftime('%Y-%m-%d %H:%M'),
                'peak_generation_power': round(peak_generation, 2),
                'average_confidence': round(forecast['confidence_level'].mean(), 2)
            },
            'generated_at': datetime.now().isoformat()
        }
        
        return clean_json_data(response_data)
        
    except Exception as e:
        logger.error(f"Error generating forecast for station {station_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate forecast: {str(e)}")


@router.get("/analytics/maintenance/{station_id}")
async def get_maintenance_recommendations(
    station_id: str,
    current_user: dict = Depends(get_current_user),
    ml_service: MLAnalyticsService = Depends(get_ml_service),
    deye_service: DeyeAPIService = Depends(get_deye_service)
):
    """Get predictive maintenance recommendations"""
    try:
        logger.info(f"Generating maintenance recommendations for station {station_id}")
        
        # Get recent performance data
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)  # 30 days for maintenance analysis
            
            history_response = await deye_service.get_station_history_data(
                station_id=station_id,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d'),
                granularity=2  # Daily data
            )
            
            performance_data = _convert_to_dataframe(history_response)
            
        except Exception as e:
            logger.warning(f"Could not fetch real data for maintenance analysis: {e}")
            # Generate mock data
            performance_data = _generate_mock_data(30)
        
        # Get maintenance recommendations
        maintenance_analysis = ml_service.maintenance_model.predict_maintenance_needs(performance_data)
        
        return {
            'success': True,
            'station_id': station_id,
            'maintenance_analysis': maintenance_analysis,
            'data_period': '30 days',
            'generated_at': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error generating maintenance recommendations for station {station_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate maintenance recommendations: {str(e)}")

@router.get("/analytics/consumption/{station_id}")
async def get_consumption_insights(
    station_id: str,
    current_user: dict = Depends(get_current_user),
    ml_service: MLAnalyticsService = Depends(get_ml_service)
):
    """Get consumption pattern insights and predictions"""
    try:
        logger.info(f"Analyzing consumption patterns for station {station_id}")
        
        # Get consumption prediction for today
        today = datetime.now().strftime('%Y-%m-%d')
        consumption_prediction = ml_service.consumption_analyzer.predict_consumption_pattern(today)
        
        # Add some additional insights
        consumption_insights = {
            'today_prediction': consumption_prediction,
            'recommendations': [
                {
                    'type': 'energy_efficiency',
                    'description': 'Consider shifting high-consumption activities to peak solar hours (10 AM - 3 PM)',
                    'potential_savings': '10-20%'
                },
                {
                    'type': 'battery_optimization',
                    'description': 'Charge battery during peak generation for evening use',
                    'potential_savings': '15-25%'
                }
            ],
            'optimal_usage_hours': ['10:00-15:00', '20:00-22:00'],
            'peak_tariff_avoidance': 'Use stored solar energy during 17:00-21:00 peak hours'
        }
        
        return {
            'success': True,
            'station_id': station_id,
            'consumption_insights': consumption_insights,
            'generated_at': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error analyzing consumption for station {station_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to analyze consumption: {str(e)}")

@router.get("/analytics/performance/{station_id}")
async def get_performance_analysis(
    station_id: str,
    days_back: int = Query(default=7, ge=1, le=30, description="Days to analyze"),
    current_user: dict = Depends(get_current_user),
    ml_service: MLAnalyticsService = Depends(get_ml_service),
    deye_service: DeyeAPIService = Depends(get_deye_service)
):
    """Get detailed performance analysis"""
    try:
        logger.info(f"Analyzing performance for station {station_id}, {days_back} days")
        
        # Get performance data
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            history_response = await deye_service.get_station_history_data(
                station_id=station_id,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d'),
                granularity=2  # Daily data for performance analysis
            )
            
            performance_data = _convert_to_dataframe(history_response)
            
        except Exception as e:
            logger.warning(f"Could not fetch real data for performance analysis: {e}")
            performance_data = _generate_mock_data(days_back)
        
        # Analyze performance
        performance_metrics = ml_service._analyze_performance(performance_data)
        
        # Add benchmarking
        performance_analysis = {
            'metrics': performance_metrics,
            'benchmarks': {
                'excellent_efficiency': '>85%',
                'good_efficiency': '70-85%',
                'poor_efficiency': '<70%',
                'current_rating': _rate_performance(performance_metrics.get('system_efficiency', 0))
            },
            'trends': {
                'generation_trend': performance_metrics.get('generation_trend', 'stable'),
                'trend_description': _describe_trend(performance_metrics.get('generation_trend', 'stable'))
            }
        }
        
        return {
            'success': True,
            'station_id': station_id,
            'analysis_period': f'{days_back} days',
            'performance_analysis': performance_analysis,
            'generated_at': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error analyzing performance for station {station_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to analyze performance: {str(e)}")

@router.get("/analytics/test-historical/{station_id}")
async def test_historical_accuracy(
    station_id: str,
    test_date: str = Query(..., description="Date to test in YYYY-MM-DD format"),
    actual_peak_kw: float = Query(..., description="Actual peak generation in kW"),
    actual_peak_time: str = Query(..., description="Actual peak time in HH:MM format"),
    current_user: dict = Depends(get_current_user),
    ml_service: MLAnalyticsService = Depends(get_ml_service)
):
    """Test ML forecast accuracy against historical actual data"""
    try:
        logger.info(f"Testing historical accuracy for station {station_id} on {test_date}")
        
        # Run historical simulation
        test_results = ml_service.forecaster.test_historical_accuracy(
            station_id, test_date, actual_peak_kw, actual_peak_time
        )
        
        # Clean data for JSON response
        clean_results = clean_json_data(test_results)
        
        return {
            'success': True,
            'station_id': station_id,
            'test_results': clean_results,
            'generated_at': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error testing historical accuracy for station {station_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to test accuracy: {str(e)}")

# Helper functions

async def _train_station_models(ml_service: MLAnalyticsService, station_id: str, days_back: int):
    """Background task to train ML models"""
    try:
        logger.info(f"Background training started for station {station_id}")
        
        # Generate training data (in real implementation, fetch from Deye API)
        training_data = _generate_mock_data(days_back)
        
        # Train models
        training_results = await ml_service.train_models(station_id, training_data)
        
        logger.info(f"Model training completed for station {station_id}: {training_results}")
        
    except Exception as e:
        logger.error(f"Model training failed for station {station_id}: {e}")

def _convert_to_dataframe(api_response: Dict[str, Any]) -> pd.DataFrame:
    """Convert Deye API response to pandas DataFrame"""
    try:
        # Extract data from API response
        if isinstance(api_response, dict) and 'data' in api_response:
            data = api_response['data']
        else:
            data = api_response
        
        # Convert to DataFrame (structure depends on API response format)
        if isinstance(data, list) and len(data) > 0:
            df = pd.DataFrame(data)
            
            # Try to set timestamp as index if available
            timestamp_cols = ['timestamp', 'time', 'date', 'datetime']
            for col in timestamp_cols:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col])
                    df.set_index(col, inplace=True)
                    break
            
            return df
        else:
            # Return empty DataFrame with common columns
            return _generate_mock_data(1).iloc[:0]
            
    except Exception as e:
        logger.warning(f"Error converting API response to DataFrame: {e}")
        return _generate_mock_data(1).iloc[:0]

def _generate_mock_data(days: int) -> pd.DataFrame:
    """Generate realistic mock solar data for demonstration"""
    import numpy as np
    
    # Create hourly timestamps
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    timestamps = pd.date_range(start=start_time, end=end_time, freq='h')
    
    # Generate realistic solar data
    data = []
    for ts in timestamps:
        hour = ts.hour
        
        # Solar generation pattern (0 at night, peak around noon)
        if 6 <= hour <= 18:
            solar_factor = np.sin(np.pi * (hour - 6) / 12)
            generation = 15.0 * solar_factor + np.random.normal(0, 1)
        else:
            generation = 0.0
        
        # Consumption pattern (higher in evening)
        if hour < 6:
            consumption = 8 + np.random.normal(0, 2)
        elif hour < 12:
            consumption = 12 + np.random.normal(0, 3)
        elif hour < 18:
            consumption = 15 + np.random.normal(0, 4)
        else:
            consumption = 25 + np.random.normal(0, 5)
        
        # Battery state of charge
        battery_soc = 50 + 30 * np.sin(2 * np.pi * hour / 24) + np.random.normal(0, 5)
        battery_soc = np.clip(battery_soc, 10, 95)
        
        # Grid power (positive = importing, negative = exporting)
        grid_power = consumption - generation
        
        data.append({
            'generation_power': max(0, generation),
            'consumption_power': max(0, consumption),
            'battery_soc': battery_soc,
            'battery_power': np.random.normal(0, 3),  # Charge/discharge
            'grid_power': grid_power
        })
    
    df = pd.DataFrame(data, index=timestamps)
    return df

def _rate_performance(efficiency: float) -> str:
    """Rate system performance"""
    if efficiency >= 0.85:
        return 'excellent'
    elif efficiency >= 0.70:
        return 'good'
    elif efficiency >= 0.50:
        return 'fair'
    else:
        return 'poor'

def _describe_trend(trend: str) -> str:
    """Describe performance trend"""
    descriptions = {
        'improving': 'System performance is improving over time',
        'declining': 'System performance shows signs of decline, consider maintenance',
        'stable': 'System performance is stable and consistent'
    }
    return descriptions.get(trend, 'Performance trend is unclear')

@router.post("/train/{station_id}")
async def train_models_with_historical_data(
    station_id: str,
    days_back: int = Query(30, description="Number of days of historical data to use for training"),
    current_user: dict = Depends(get_current_user),
    ml_service: MLAnalyticsService = Depends(get_ml_service)
):
    """
    Train ML models using actual historical solar data from Deye API
    This endpoint provides detailed progress tracking and training metrics
    """
    try:
        logger.info(f"Starting ML model training for station {station_id} with {days_back} days of data")
        
        # Initialize training progress tracker
        training_progress = {
            'status': 'initializing',
            'stage': 'data_collection',
            'progress_percent': 0,
            'current_task': 'Collecting historical data from Deye API',
            'stages_completed': [],
            'errors': [],
            'metrics': {},
            'started_at': datetime.now().isoformat()
        }
        
        # Step 1: Collect historical data using progressive approach
        training_progress.update({
            'status': 'running',
            'progress_percent': 10,
            'current_task': 'Collecting historical data from all available sources'
        })

        # Use the new historical data service for progressive data collection
        historical_service = HistoricalDataService()

        try:
            # Collect data progressively from all available sources
            collection_result = await historical_service.collect_historical_data(
                station_id=station_id,
                days_back=days_back,
                force_refresh=True  # Always get fresh data for training
            )

            training_progress.update({
                'progress_percent': 30,
                'current_task': f'Data collection completed: {collection_result.solar_records} solar + {collection_result.weather_records} weather records'
            })

            if not collection_result.success:
                error_msg = collection_result.error_message or "Failed to collect any historical data"
                logger.error(f"Progressive data collection failed: {error_msg}")
                training_progress.update({
                    'status': 'failed',
                    'current_task': f'Data collection failed: {error_msg}',
                    'error': error_msg
                })
                return clean_json_data({
                    'status': 'failed',
                    'error': error_msg,
                    'station_id': station_id,
                    'failed_at': datetime.now().isoformat()
                })

            df = collection_result.combined_dataframe

            # Log data collection summary
            data_summary = {
                'total_records': len(df),
                'solar_records': collection_result.solar_records,
                'weather_records': collection_result.weather_records,
                'date_range': collection_result.date_range,
                'available_features': list(df.columns)
            }
            logger.info(f"Progressive data collection successful: {data_summary}")

            # Update progress with data quality information
            training_progress.update({
                'progress_percent': 35,
                'current_task': f'Data quality: {len(df)} total records with {len(df.columns)} features',
                'data_summary': data_summary
            })
            # Data collection is now handled by the progressive service above
            # No fallback to mock data - we work with whatever real data is available


            # Update training progress with final data preparation
            training_progress.update({
                'stage': 'data_preparation',
                'progress_percent': 40,
                'current_task': f'Data preparation complete: {len(df)} records ready for training',
                'stages_completed': ['data_collection'],
                'metrics': {
                    'data_points_collected': len(df),
                    'date_range': f"{df.index.min()} to {df.index.max()}",
                    'features_available': list(df.columns),
                    'data_sources': {
                        'solar_records': data_summary['solar_records'],
                        'weather_records': data_summary['weather_records']
                    }
                }
            })

        finally:
            # Clean up historical service resources if needed
            pass
        
        # Step 2: Train Production Forecaster
        training_progress.update({
            'stage': 'forecaster_training',
            'progress_percent': 40,
            'current_task': 'Training solar production forecasting model'
        })
        
        forecaster_results = ml_service.forecaster.train_daily_model(df)
        training_progress['stages_completed'].append('forecaster_training')
        training_progress['metrics']['forecaster'] = forecaster_results
        
        # Step 3: Train Consumption Analyzer
        training_progress.update({
            'stage': 'consumption_training',
            'progress_percent': 70,
            'current_task': 'Analyzing consumption patterns using K-Means clustering'
        })
        
        consumption_results = ml_service.consumption_analyzer.analyze_daily_patterns(df)
        training_progress['stages_completed'].append('consumption_training')
        training_progress['metrics']['consumption_analyzer'] = consumption_results
        
        # Step 4: Train Maintenance Model
        training_progress.update({
            'stage': 'maintenance_training',
            'progress_percent': 85,
            'current_task': 'Training predictive maintenance model using Random Forest'
        })
        
        maintenance_results = ml_service.maintenance_model.train_degradation_model(df)
        training_progress['stages_completed'].append('consumption_training')
        training_progress['metrics']['maintenance_model'] = maintenance_results
        
        # Step 6: Save Models
        training_progress.update({
            'stage': 'saving_models',
            'progress_percent': 95,
            'current_task': 'Saving trained models to disk'
        })
        
        try:
            ml_service.save_models()
            models_saved = True
            save_error = None
        except Exception as e:
            models_saved = False
            save_error = str(e)
            training_progress['errors'].append(f"Model saving error: {e}")
        
        # Complete training
        training_progress.update({
            'status': 'completed',
            'stage': 'finished',
            'progress_percent': 100,
            'current_task': 'Training completed successfully',
            'stages_completed': ['data_preparation', 'forecaster_training', 'consumption_training', 'maintenance_training', 'saving_models'],
            'completed_at': datetime.now().isoformat(),
            'models_saved': models_saved
        })
        
        if save_error:
            training_progress['save_error'] = save_error
        
        # Calculate training summary
        total_training_samples = sum([
            forecaster_results.get('training_samples', 0),
            consumption_results.get('daily_profiles_count', 0),
            maintenance_results.get('training_samples', 0)
        ])
        
        training_summary = {
            'station_id': station_id,
            'training_progress': training_progress,
            'training_summary': {
                'total_data_points': len(df),
                'total_training_samples': total_training_samples,
                'models_trained': 3,
                'features_used': list(df.columns),
                'training_duration': (datetime.now() - datetime.fromisoformat(training_progress['started_at'])).total_seconds(),
                'success_rate': len([r for r in [forecaster_results, consumption_results, maintenance_results] if 'error' not in r]) / 3 * 100
            },
            'model_metrics': training_progress['metrics']
        }
        
        logger.info(f"ML training completed for station {station_id}")
        return clean_json_data(training_summary)
        
    except Exception as e:
        logger.error(f"Error during ML training: {e}")
        return clean_json_data({
            'status': 'failed',
            'error': str(e),
            'station_id': station_id,
            'failed_at': datetime.now().isoformat()
        })

@router.get("/training-status/{station_id}")
async def get_training_status(
    station_id: str,
    current_user: dict = Depends(get_current_user),
    ml_service: MLAnalyticsService = Depends(get_ml_service)
):
    """Get the current status of model training and available trained models"""
    try:
        # Check which models are available
        models_status = {
            'forecaster_model': ml_service.forecaster.daily_model is not None,
            'consumption_model': ml_service.consumption_analyzer.kmeans is not None,
            'maintenance_model': ml_service.maintenance_model.degradation_model is not None
        }
        
        # Check saved model files
        saved_models = {}
        for model_file in ml_service.models_dir.glob("*.joblib"):
            saved_models[model_file.stem] = {
                'file_path': str(model_file),
                'file_size': model_file.stat().st_size,
                'last_modified': datetime.fromtimestamp(model_file.stat().st_mtime).isoformat()
            }
        
        return clean_json_data({
            'station_id': station_id,
            'models_loaded': models_status,
            'total_models_loaded': sum(models_status.values()),
            'saved_model_files': saved_models,
            'models_directory': str(ml_service.models_dir),
            'ready_for_predictions': all(models_status.values())
        })
        
    except Exception as e:
        logger.error(f"Error getting training status: {e}")
        return clean_json_data({'error': str(e)})

@router.get("/test")
async def test_endpoint():
    """Simple test endpoint to verify routing works"""
    return {"message": "ML Analytics router is working", "timestamp": datetime.now().isoformat()}
