"""
Data Collection Service for Solar Display Application

Automated data collection pipeline that:
- Fetches solar production data from Deye API every 15 minutes
- Collects weather data from Open-Meteo API  
- Stores data in SQLite database for ML training
- Generates ML features for model training
- Handles errors and retries gracefully

This service transforms the system from monitoring-only to data-driven
AI analytics by providing the historical data needed for real ML insights.

READ-ONLY SAFETY: Only uses GET endpoints from Deye API - never modifies inverter settings
"""

import asyncio
import schedule
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from src.database import (
    db, SolarProductionData, WeatherData, MLFeatures,
    get_season_from_date, get_time_of_day, categorize_weather
)
from src.services.deye_service import DeyeAPIService, APIError
from src.services.weather_service import OpenMeteoService, get_weather_service
import numpy as np
import math

logger = logging.getLogger(__name__)

@dataclass
class CollectionStats:
    """Statistics for data collection monitoring"""
    successful_collections: int = 0
    failed_collections: int = 0
    last_collection_time: Optional[datetime] = None
    last_error: Optional[str] = None
    total_solar_records: int = 0
    total_weather_records: int = 0

class DataCollectionService:
    """
    Automated data collection service for solar analytics
    
    Collects data from multiple sources and prepares it for ML training:
    - Deye solar inverter data (15-minute intervals)
    - Open-Meteo weather data (hourly updates)
    - Feature engineering for ML models
    """
    
    def __init__(self):
        self.deye_service: Optional[DeyeAPIService] = None
        self.weather_service: Optional[OpenMeteoService] = None
        self.is_running = False
        self.collection_interval_minutes = 15
        self.stats = CollectionStats()
        
    async def initialize(self):
        """Initialize API services"""
        try:
            self.deye_service = DeyeAPIService()
            self.weather_service = await get_weather_service()
            logger.info("Data collection service initialized")
        except Exception as e:
            logger.error(f"Failed to initialize data collection service: {e}")
            raise
    
    async def collect_solar_data(self, station_id: str) -> Optional[SolarProductionData]:
        """
        Collect current solar production data from Deye API
        
        Args:
            station_id: Deye station identifier
            
        Returns:
            SolarProductionData if successful, None if failed
        """
        try:
            logger.debug(f"Collecting solar data for station {station_id}")
            
            # Get latest data from Deye API (READ-ONLY operation)
            latest_data = await self.deye_service.get_station_latest_data(station_id)
            
            # Transform Deye API response to our data model
            solar_data = SolarProductionData(
                timestamp=datetime.now(),
                station_id=station_id,
                total_power=latest_data.get('generationPower', 0.0) or 0.0,
                daily_energy=latest_data.get('dailyEnergy', 0.0) or 0.0,
                battery_soc=latest_data.get('batterySOC', latest_data.get('batterySoc', 0.0)) or 0.0,
                battery_power=latest_data.get('batteryPower', 0.0) or 0.0,
                grid_power=latest_data.get('wirePower', latest_data.get('gridPower', 0.0)) or 0.0,
                consumption_power=latest_data.get('consumptionPower', 0.0) or 0.0,
                irradiate_intensity=latest_data.get('irradiateIntensity'),
                efficiency=self._calculate_efficiency(latest_data),
                raw_data=json.dumps(latest_data)
            )
            
            logger.debug(f"Solar data collected: {solar_data.total_power}kW, {solar_data.efficiency}% efficiency")
            return solar_data
            
        except APIError as e:
            logger.error(f"Deye API error collecting solar data: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error collecting solar data: {e}")
            return None
    
    async def collect_weather_data(self) -> Optional[WeatherData]:
        """
        Collect current weather data from Open-Meteo API
        
        Returns:
            WeatherData if successful, None if failed
        """
        try:
            logger.debug("Collecting weather data for Pretoria")
            
            # Get current weather (READ-ONLY operation)
            weather_data = await self.weather_service.get_current_weather()
            
            logger.debug(f"Weather data collected: {weather_data.temperature}°C, {weather_data.solar_radiation}W/m²")
            return weather_data
            
        except Exception as e:
            logger.error(f"Error collecting weather data: {e}")
            return None
    
    async def store_solar_data(self, solar_data: SolarProductionData):
        """
        Store solar production data in database
        
        Args:
            solar_data: Solar data to store
        """
        try:
            await db.execute_insert(
                """INSERT INTO solar_production 
                   (timestamp, station_id, total_power, daily_energy, inverter_temp,
                    dc_voltage, ac_voltage, efficiency, grid_frequency, battery_soc,
                    battery_power, grid_power, consumption_power, irradiate_intensity,
                    error_code, raw_data)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    solar_data.timestamp.isoformat(),
                    solar_data.station_id,
                    solar_data.total_power,
                    solar_data.daily_energy,
                    solar_data.inverter_temp,
                    solar_data.dc_voltage,
                    solar_data.ac_voltage,
                    solar_data.efficiency,
                    solar_data.grid_frequency,
                    solar_data.battery_soc,
                    solar_data.battery_power,
                    solar_data.grid_power,
                    solar_data.consumption_power,
                    solar_data.irradiate_intensity,
                    solar_data.error_code,
                    solar_data.raw_data
                )
            )
            
            self.stats.total_solar_records += 1
            logger.debug(f"Solar data stored for {solar_data.timestamp}")
            
        except Exception as e:
            logger.error(f"Failed to store solar data: {e}")
            raise
    
    async def store_weather_data(self, weather_data: WeatherData):
        """
        Store weather data in database
        
        Args:
            weather_data: Weather data to store
        """
        try:
            # Use the weather service's caching method
            await self.weather_service.cache_weather_data(weather_data)
            self.stats.total_weather_records += 1
            
        except Exception as e:
            logger.error(f"Failed to store weather data: {e}")
            raise
    
    async def generate_ml_features(self, solar_data: SolarProductionData, weather_data: Optional[WeatherData]):
        """
        Generate ML features from solar and weather data
        
        Args:
            solar_data: Solar production data
            weather_data: Weather data (can be None)
        """
        try:
            timestamp = solar_data.timestamp
            
            # Time-based features with cyclical encoding
            hour_angle = 2 * math.pi * timestamp.hour / 24
            day_angle = 2 * math.pi * timestamp.timetuple().tm_yday / 365
            
            # Use weather data if available, otherwise estimate/use defaults
            if weather_data:
                temperature = weather_data.temperature
                irradiance = weather_data.solar_radiation
                cloud_cover = weather_data.cloud_cover
                weather_category = categorize_weather(cloud_cover, weather_data.weather_code)
            else:
                # Estimate weather from solar production patterns
                temperature = self._estimate_temperature(timestamp, solar_data.total_power)
                irradiance = solar_data.irradiate_intensity or self._estimate_irradiance(timestamp, solar_data.total_power)
                cloud_cover = self._estimate_cloud_cover(timestamp, solar_data.total_power, irradiance)
                weather_category = categorize_weather(cloud_cover)
            
            ml_features = MLFeatures(
                timestamp=timestamp,
                production_kw=solar_data.total_power,
                irradiance_wm2=irradiance,
                temperature_c=temperature,
                cloud_cover_pct=cloud_cover,
                efficiency_pct=solar_data.efficiency or 0.0,
                season=get_season_from_date(timestamp),
                time_of_day=get_time_of_day(timestamp),
                weather_category=weather_category,
                hour_sin=math.sin(hour_angle),
                hour_cos=math.cos(hour_angle),
                day_of_year_sin=math.sin(day_angle),
                day_of_year_cos=math.cos(day_angle)
            )
            
            # Store ML features
            await db.execute_insert(
                """INSERT OR REPLACE INTO ml_features
                   (timestamp, production_kw, irradiance_wm2, temperature_c, cloud_cover_pct,
                    efficiency_pct, season, time_of_day, weather_category,
                    hour_sin, hour_cos, day_of_year_sin, day_of_year_cos)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    ml_features.timestamp.isoformat(),
                    ml_features.production_kw,
                    ml_features.irradiance_wm2,
                    ml_features.temperature_c,
                    ml_features.cloud_cover_pct,
                    ml_features.efficiency_pct,
                    ml_features.season.value,
                    ml_features.time_of_day.value,
                    ml_features.weather_category.value,
                    ml_features.hour_sin,
                    ml_features.hour_cos,
                    ml_features.day_of_year_sin,
                    ml_features.day_of_year_cos
                )
            )
            
            logger.debug(f"ML features generated for {timestamp}")
            
        except Exception as e:
            logger.error(f"Failed to generate ML features: {e}")
            # Don't raise - feature generation failure shouldn't break data collection
    
    async def collect_and_store_data(self):
        """
        Main data collection routine - collects from all sources and stores
        
        This is the core function called every 15 minutes to build
        the historical dataset needed for ML training.
        """
        collection_start = datetime.now()
        logger.info("Starting scheduled data collection...")
        
        try:
            # Get list of solar stations
            stations_raw = await self.deye_service.get_station_list()
            
            # Handle different response formats from Deye API
            if isinstance(stations_raw, dict):
                station_list = stations_raw.get('stationList', [])
            elif isinstance(stations_raw, list):
                station_list = stations_raw
            else:
                station_list = []
            
            if not station_list:
                logger.warning("No solar stations found for data collection")
                return
            
            # Collect weather data once (shared across all stations)
            weather_data = await self.collect_weather_data()
            if weather_data:
                await self.store_weather_data(weather_data)
            
            # Collect solar data for each station
            for station in station_list:
                station_id = str(station.get('id', station.get('stationId', '')))
                if not station_id:
                    logger.warning("Station found without ID, skipping")
                    continue
                
                # Collect solar data
                solar_data = await self.collect_solar_data(station_id)
                if solar_data:
                    await self.store_solar_data(solar_data)
                    
                    # Generate ML features
                    await self.generate_ml_features(solar_data, weather_data)
                
                # Small delay between stations to be respectful to API
                await asyncio.sleep(1)
            
            # Update statistics
            self.stats.successful_collections += 1
            self.stats.last_collection_time = collection_start
            
            collection_duration = (datetime.now() - collection_start).total_seconds()
            logger.info(f"Data collection completed successfully in {collection_duration:.2f}s")
            
        except Exception as e:
            self.stats.failed_collections += 1
            self.stats.last_error = str(e)
            logger.error(f"Data collection failed: {e}")
            
        finally:
            # Cleanup
            if self.deye_service:
                await self.deye_service.close_session()
    
    async def start_automated_collection(self):
        """
        Start automated data collection every 15 minutes
        
        This enables continuous data gathering for ML model training.
        """
        if self.is_running:
            logger.warning("Data collection is already running")
            return
        
        logger.info(f"Starting automated data collection (every {self.collection_interval_minutes} minutes)")
        self.is_running = True
        
        # Schedule collection every 15 minutes
        schedule.every(self.collection_interval_minutes).minutes.do(
            lambda: asyncio.create_task(self.collect_and_store_data())
        )
        
        # Run initial collection
        await self.collect_and_store_data()
        
        # Keep scheduler running
        while self.is_running:
            schedule.run_pending()
            await asyncio.sleep(60)  # Check every minute
    
    def stop_automated_collection(self):
        """Stop automated data collection"""
        logger.info("Stopping automated data collection")
        self.is_running = False
        schedule.clear()
    
    async def sync_historical_data(self, days_back: int = 30):
        """
        Sync historical data to jumpstart ML training
        
        Args:
            days_back: Number of days of historical data to collect
        """
        logger.info(f"Starting historical data sync for {days_back} days")
        
        try:
            # Collect historical weather data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            historical_weather = await self.weather_service.get_historical_weather(
                start_date.strftime("%Y-%m-%d"),
                end_date.strftime("%Y-%m-%d")
            )
            
            # Store historical weather data
            for weather_data in historical_weather:
                await self.store_weather_data(weather_data)
            
            logger.info(f"Synced {len(historical_weather)} historical weather records")
            
            # Note: Deye API may not provide extensive historical data
            # The system will build historical solar data going forward
            
        except Exception as e:
            logger.error(f"Historical data sync failed: {e}")
            raise
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get data collection statistics"""
        return {
            "successful_collections": self.stats.successful_collections,
            "failed_collections": self.stats.failed_collections,
            "last_collection_time": self.stats.last_collection_time.isoformat() if self.stats.last_collection_time else None,
            "last_error": self.stats.last_error,
            "total_solar_records": self.stats.total_solar_records,
            "total_weather_records": self.stats.total_weather_records,
            "is_running": self.is_running,
            "collection_interval_minutes": self.collection_interval_minutes
        }
    
    # =================== HELPER METHODS ===================
    
    def _calculate_efficiency(self, deye_data: Dict[str, Any]) -> Optional[float]:
        """Calculate system efficiency from Deye data"""
        try:
            power = deye_data.get('generationPower', 0.0) or 0.0
            irradiance = deye_data.get('irradiateIntensity') or 0.0
            
            if irradiance > 50:  # Only calculate during sufficient sunlight
                # Assume 5kW system capacity (adjust based on actual system)
                rated_capacity = 5.0  # kW
                standard_irradiance = 1000  # W/m²
                
                expected_power = rated_capacity * (irradiance / standard_irradiance)
                if expected_power > 0:
                    efficiency = (power / expected_power) * 100
                    return min(efficiency, 100.0)  # Cap at 100%
            
            return None
            
        except (TypeError, ZeroDivisionError):
            return None
    
    def _estimate_temperature(self, timestamp: datetime, solar_power: float) -> float:
        """Estimate temperature based on time and solar production (fallback)"""
        # Simple estimation for Pretoria winter (current season)
        hour = timestamp.hour
        base_temp = 15.0  # Average July temperature in Pretoria
        
        # Daily temperature variation
        if 6 <= hour <= 18:  # Daytime
            temp_variation = 5.0 * math.sin((hour - 6) * math.pi / 12)
            solar_factor = min(solar_power * 2, 5.0)  # Solar heating effect
            return base_temp + temp_variation + solar_factor
        else:  # Nighttime
            return base_temp - 3.0
    
    def _estimate_irradiance(self, timestamp: datetime, solar_power: float) -> float:
        """Estimate solar irradiance based on time and production"""
        hour = timestamp.hour
        
        if hour < 6 or hour > 18:
            return 0.0
        
        # Peak irradiance around noon
        if 6 <= hour <= 18:
            # Simple bell curve for daily irradiance
            peak_hour = 12
            hours_from_peak = abs(hour - peak_hour)
            max_irradiance = 600  # Winter peak in Pretoria
            
            irradiance = max_irradiance * math.exp(-(hours_from_peak ** 2) / 8)
            
            # Adjust based on actual solar production
            if solar_power > 0:
                irradiance = max(irradiance, solar_power * 200)  # Rough conversion
            
            return irradiance
        
        return 0.0
    
    def _estimate_cloud_cover(self, timestamp: datetime, solar_power: float, expected_irradiance: float) -> float:
        """Estimate cloud cover based on solar production vs expected"""
        if expected_irradiance < 50:  # Night or very early/late
            return 50.0  # Default assumption
        
        # Compare actual production to expected clear-sky production
        expected_power = expected_irradiance / 200  # Rough conversion
        
        if expected_power > 0:
            power_ratio = solar_power / expected_power
            # More cloud cover if production is lower than expected
            cloud_cover = max(0, 100 * (1 - power_ratio))
            return min(cloud_cover, 100.0)
        
        return 50.0  # Default assumption

# Global instance
data_collection_service = DataCollectionService()

async def get_data_collection_service() -> DataCollectionService:
    """Dependency injection for data collection service"""
    return data_collection_service
