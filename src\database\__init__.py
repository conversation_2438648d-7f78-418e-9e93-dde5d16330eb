"""
Database package initialization for Solar Display

Provides easy imports for database components:
- Connection management  
- Data models
- Migration utilities
"""

from .connection import db, get_db_connection, DatabaseConnection
from .models import (
    SolarProductionData,
    WeatherData, 
    MLFeatures,
    Season,
    TimeOfDay,
    WeatherCategory,
    get_season_from_date,
    get_time_of_day,
    categorize_weather
)
from .migrations import (
    initialize_database,
    create_database_schema,
    check_database_exists,
    get_database_stats,
    backup_database,
    clear_old_data
)

__all__ = [
    # Connection
    'db',
    'get_db_connection', 
    'DatabaseConnection',
    
    # Models
    'SolarProductionData',
    'WeatherData',
    'MLFeatures', 
    'Season',
    'TimeOfDay',
    'WeatherCategory',
    'get_season_from_date',
    'get_time_of_day',
    'categorize_weather',
    
    # Migrations
    'initialize_database',
    'create_database_schema',
    'check_database_exists', 
    'get_database_stats',
    'backup_database',
    'clear_old_data'
]
