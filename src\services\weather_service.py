"""
Weather Service for Solar Display - Open-Meteo API Integration

Provides weather data for solar energy forecasting in Villieria, Pretoria, South Africa.
Integrates with Open-Meteo API to fetch current conditions, forecasts, and historical data.

Location: -25.749°S, 28.231°E (Villieria, Pretoria)
Timezone: Africa/Johannesburg

Key features:
- Current weather conditions
- 7-day solar irradiance forecasts  
- Historical weather data for ML training
- Rate limiting and error handling
- Data caching to minimize API calls
"""

import httpx
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from src.database import db, WeatherData, categorize_weather

logger = logging.getLogger(__name__)

# Pretoria, Villieria coordinates from PRD.md
PRETORIA_LATITUDE = -25.749
PRETORIA_LONGITUDE = 28.231
PRETORIA_TIMEZONE = "Africa/Johannesburg"

@dataclass
class OpenMeteoConfig:
    """Configuration for Open-Meteo API calls"""
    base_url: str = "https://api.open-meteo.com/v1"
    historical_url: str = "https://archive-api.open-meteo.com/v1"
    latitude: float = PRETORIA_LATITUDE
    longitude: float = PRETORIA_LONGITUDE
    timezone: str = PRETORIA_TIMEZONE
    
    # Current and forecast variables
    hourly_variables: List[str] = None
    daily_variables: List[str] = None
    
    def __post_init__(self):
        if self.hourly_variables is None:
            self.hourly_variables = [
                "temperature_2m",           # Air temperature (°C)
                "relative_humidity_2m",     # Relative humidity (%)
                "precipitation",            # Precipitation (mm)
                "cloud_cover",             # Cloud coverage (%)
                "wind_speed_10m",          # Wind speed at 10m (km/h -> convert to m/s)
                "surface_pressure",        # Surface pressure (hPa)
                "shortwave_radiation",     # Solar irradiance (W/m²)
                "direct_radiation",        # Direct solar radiation (W/m²)
                "diffuse_radiation",       # Diffuse solar radiation (W/m²)
                "weather_code"             # WMO weather code
            ]
        
        if self.daily_variables is None:
            self.daily_variables = [
                "temperature_2m_max",
                "temperature_2m_min",
                "sunshine_duration",
                "precipitation_sum",
                "wind_speed_10m_max"
            ]

class OpenMeteoService:
    """
    Open-Meteo weather API service for solar energy applications
    
    Provides weather data specifically for solar production forecasting
    and ML model training in the Pretoria area.
    """
    
    def __init__(self):
        self.config = OpenMeteoConfig()
        self.session: Optional[httpx.AsyncClient] = None
        self.last_request_time: Optional[datetime] = None
        self.min_request_interval = 1.0  # Minimum 1 second between requests
        
    async def _get_session(self) -> httpx.AsyncClient:
        """Get or create async HTTP session"""
        if self.session is None or self.session.is_closed:
            self.session = httpx.AsyncClient(
                timeout=30.0,
                headers={
                    "User-Agent": "SolarDisplay/1.0 (Pretoria Solar Analytics)",
                }
            )
        return self.session
    
    async def close_session(self):
        """Close HTTP session"""
        if self.session and not self.session.is_closed:
            await self.session.aclose()
    
    async def _rate_limit(self):
        """Simple rate limiting to be respectful to Open-Meteo API"""
        if self.last_request_time:
            elapsed = (datetime.now() - self.last_request_time).total_seconds()
            if elapsed < self.min_request_interval:
                await asyncio.sleep(self.min_request_interval - elapsed)
        self.last_request_time = datetime.now()
    
    async def _make_request(self, url: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make HTTP request to Open-Meteo API with error handling
        
        Args:
            url: API endpoint URL
            params: Query parameters
            
        Returns:
            API response data
            
        Raises:
            Exception: If API request fails
        """
        await self._rate_limit()
        
        session = await self._get_session()
        
        try:
            logger.debug(f"Open-Meteo API request: {url} with params: {params}")
            response = await session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            logger.debug(f"Open-Meteo API response received: {len(str(data))} characters")
            return data
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Open-Meteo API HTTP error: {e.response.status_code} - {e.response.text}")
            raise Exception(f"Weather API error: {e.response.status_code}")
        except httpx.RequestError as e:
            logger.error(f"Open-Meteo API request error: {e}")
            raise Exception(f"Weather API connection error: {e}")
        except Exception as e:
            logger.error(f"Open-Meteo API unexpected error: {e}")
            raise
    
    async def get_current_weather(self) -> WeatherData:
        """
        Get current weather conditions for Pretoria
        
        Returns:
            WeatherData model with current conditions
        """
        logger.info("Fetching current weather for Pretoria")
        
        params = {
            "latitude": self.config.latitude,
            "longitude": self.config.longitude,
            "timezone": self.config.timezone,
            "hourly": ",".join(self.config.hourly_variables),
            "forecast_days": 1,
            "current": "temperature_2m,relative_humidity_2m,cloud_cover,wind_speed_10m,weather_code"
        }
        
        try:
            data = await self._make_request(f"{self.config.base_url}/forecast", params)
            
            # Extract current data
            current = data.get("current", {})
            current_time = current.get("time", datetime.now().isoformat())
            
            # Get the most recent hourly data as backup
            hourly = data.get("hourly", {})
            if hourly and hourly.get("time"):
                # Use first hourly entry as current
                hourly_idx = 0
                current_time = hourly["time"][hourly_idx]
                
                weather_data = WeatherData(
                    timestamp=datetime.fromisoformat(current_time.replace('Z', '+00:00')),
                    temperature=current.get("temperature_2m", hourly.get("temperature_2m", [0])[hourly_idx]),
                    humidity=current.get("relative_humidity_2m", hourly.get("relative_humidity_2m", [0])[hourly_idx]),
                    solar_radiation=hourly.get("shortwave_radiation", [0])[hourly_idx],
                    cloud_cover=current.get("cloud_cover", hourly.get("cloud_cover", [0])[hourly_idx]),
                    wind_speed=current.get("wind_speed_10m", hourly.get("wind_speed_10m", [0])[hourly_idx]) / 3.6,  # km/h to m/s
                    pressure=hourly.get("surface_pressure", [None])[hourly_idx],
                    weather_code=current.get("weather_code", hourly.get("weather_code", [None])[hourly_idx]),
                    source="open-meteo"
                )
            else:
                # Fallback to current data only
                weather_data = WeatherData(
                    timestamp=datetime.fromisoformat(current_time.replace('Z', '+00:00')) if 'T' in current_time else datetime.now(),
                    temperature=current.get("temperature_2m", 20.0),  # Default fallback
                    humidity=current.get("relative_humidity_2m", 50.0),
                    solar_radiation=0.0,  # Will be 0 at night anyway
                    cloud_cover=current.get("cloud_cover", 50.0),
                    wind_speed=current.get("wind_speed_10m", 0.0) / 3.6,  # km/h to m/s
                    pressure=None,
                    weather_code=current.get("weather_code"),
                    source="open-meteo"
                )
            
            logger.info(f"Current weather: {weather_data.temperature}°C, {weather_data.cloud_cover}% clouds, {weather_data.solar_radiation}W/m²")
            return weather_data
            
        except Exception as e:
            logger.error(f"Failed to get current weather: {e}")
            raise
    
    async def get_forecast(self, days: int = 7) -> List[WeatherData]:
        """
        Get weather forecast for solar production planning
        
        Args:
            days: Number of days to forecast (max 16 for free tier)
            
        Returns:
            List of WeatherData for hourly forecasts
        """
        logger.info(f"Fetching {days}-day weather forecast for Pretoria")
        
        if days > 16:
            logger.warning("Open-Meteo free tier limited to 16 days, adjusting forecast period")
            days = 16
        
        params = {
            "latitude": self.config.latitude,
            "longitude": self.config.longitude,
            "timezone": self.config.timezone,
            "hourly": ",".join(self.config.hourly_variables),
            "forecast_days": days
        }
        
        try:
            data = await self._make_request(f"{self.config.base_url}/forecast", params)
            
            hourly = data.get("hourly", {})
            if not hourly or not hourly.get("time"):
                raise Exception("No hourly forecast data received")
            
            weather_list = []
            time_list = hourly["time"]
            
            for i in range(len(time_list)):
                weather_data = WeatherData(
                    timestamp=datetime.fromisoformat(time_list[i]),
                    temperature=self._get_hourly_value(hourly, "temperature_2m", i, 20.0),
                    humidity=self._get_hourly_value(hourly, "relative_humidity_2m", i, 50.0),
                    solar_radiation=self._get_hourly_value(hourly, "shortwave_radiation", i, 0.0),
                    cloud_cover=self._get_hourly_value(hourly, "cloud_cover", i, 50.0),
                    wind_speed=self._get_hourly_value(hourly, "wind_speed_10m", i, 0.0) / 3.6,  # km/h to m/s
                    pressure=self._get_hourly_value(hourly, "surface_pressure", i, None),
                    weather_code=self._get_hourly_value(hourly, "weather_code", i, None),
                    source="open-meteo"
                )
                weather_list.append(weather_data)
            
            logger.info(f"Retrieved {len(weather_list)} hourly forecast entries")
            return weather_list
            
        except Exception as e:
            logger.error(f"Failed to get weather forecast: {e}")
            raise
    
    async def get_historical_weather(self, start_date: str, end_date: str) -> List[WeatherData]:
        """
        Get historical weather data for ML model training
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            
        Returns:
            List of historical WeatherData
        """
        logger.info(f"Fetching historical weather data from {start_date} to {end_date}")
        
        params = {
            "latitude": self.config.latitude,
            "longitude": self.config.longitude,
            "start_date": start_date,
            "end_date": end_date,
            "timezone": self.config.timezone,
            "hourly": ",".join(self.config.hourly_variables)
        }
        
        try:
            data = await self._make_request(f"{self.config.historical_url}/archive", params)
            
            hourly = data.get("hourly", {})
            if not hourly or not hourly.get("time"):
                logger.warning(f"No historical weather data available for {start_date} to {end_date}")
                return []
            
            weather_list = []
            time_list = hourly["time"]
            
            for i in range(len(time_list)):
                weather_data = WeatherData(
                    timestamp=datetime.fromisoformat(time_list[i]),
                    temperature=self._get_hourly_value(hourly, "temperature_2m", i, 20.0),
                    humidity=self._get_hourly_value(hourly, "relative_humidity_2m", i, 50.0),
                    solar_radiation=self._get_hourly_value(hourly, "shortwave_radiation", i, 0.0),
                    cloud_cover=self._get_hourly_value(hourly, "cloud_cover", i, 50.0),
                    wind_speed=self._get_hourly_value(hourly, "wind_speed_10m", i, 0.0) / 3.6,
                    pressure=self._get_hourly_value(hourly, "surface_pressure", i, None),
                    weather_code=self._get_hourly_value(hourly, "weather_code", i, None),
                    source="open-meteo"
                )
                weather_list.append(weather_data)
            
            logger.info(f"Retrieved {len(weather_list)} historical weather entries")
            return weather_list
            
        except Exception as e:
            logger.error(f"Failed to get historical weather: {e}")
            raise
    
    def _get_hourly_value(self, hourly_data: Dict, variable: str, index: int, default: Any) -> Any:
        """Safely extract hourly value with fallback"""
        try:
            values = hourly_data.get(variable, [])
            if index < len(values) and values[index] is not None:
                return values[index]
            return default
        except (IndexError, TypeError):
            return default
    
    async def cache_weather_data(self, weather_data: WeatherData):
        """
        Cache weather data to database for offline access
        
        Args:
            weather_data: WeatherData to cache
        """
        try:
            # Check if data already exists for this timestamp
            existing = await db.execute_query(
                "SELECT id FROM weather_data WHERE timestamp = ?",
                (weather_data.timestamp.isoformat(),)
            )
            
            if existing:
                logger.debug(f"Weather data already cached for {weather_data.timestamp}")
                return
            
            # Insert new weather data
            await db.execute_insert(
                """INSERT INTO weather_data 
                   (timestamp, temperature, humidity, solar_radiation, cloud_cover, 
                    wind_speed, pressure, weather_code, source)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    weather_data.timestamp.isoformat(),
                    weather_data.temperature,
                    weather_data.humidity,
                    weather_data.solar_radiation,
                    weather_data.cloud_cover,
                    weather_data.wind_speed,
                    weather_data.pressure,
                    weather_data.weather_code,
                    weather_data.source
                )
            )
            
            logger.debug(f"Cached weather data for {weather_data.timestamp}")
            
        except Exception as e:
            logger.error(f"Failed to cache weather data: {e}")
            # Don't raise - caching failure shouldn't break the main flow
    
    async def get_cached_weather(self, start_time: datetime, end_time: datetime) -> List[WeatherData]:
        """
        Retrieve cached weather data from database
        
        Args:
            start_time: Start of time range
            end_time: End of time range
            
        Returns:
            List of cached WeatherData
        """
        try:
            rows = await db.execute_query(
                """SELECT timestamp, temperature, humidity, solar_radiation, cloud_cover,
                          wind_speed, pressure, weather_code, source
                   FROM weather_data 
                   WHERE timestamp BETWEEN ? AND ?
                   ORDER BY timestamp""",
                (start_time.isoformat(), end_time.isoformat())
            )
            
            weather_list = []
            for row in rows:
                weather_data = WeatherData(
                    timestamp=datetime.fromisoformat(row["timestamp"]),
                    temperature=row["temperature"],
                    humidity=row["humidity"],
                    solar_radiation=row["solar_radiation"],
                    cloud_cover=row["cloud_cover"],
                    wind_speed=row["wind_speed"],
                    pressure=row["pressure"],
                    weather_code=row["weather_code"],
                    source=row["source"]
                )
                weather_list.append(weather_data)
            
            logger.info(f"Retrieved {len(weather_list)} cached weather entries")
            return weather_list
            
        except Exception as e:
            logger.error(f"Failed to get cached weather data: {e}")
            return []

# Global instance
weather_service = OpenMeteoService()

async def get_weather_service() -> OpenMeteoService:
    """Dependency injection for weather service"""
    return weather_service
