from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>eader

from src.config import settings
from src.models.auth_models import AccessCode

# This is a very simple authentication scheme.
# In a real-world application, you would use OAuth2 with a proper user database.
ACCESS_CODE = settings.access_code

def verify_access_code(code: str) -> bool:
    """
    Verifies the access code.
    In a real application, this would check against a database of users or codes.
    """
    return code == ACCESS_CODE

# For protecting endpoints with an access code in a header
api_key_header = APIKeyHeader(name="X-Access-Code", auto_error=False)

async def get_current_user(access_code: str = Depends(api_key_header)):
    """
    Dependency to protect endpoints.
    Checks for a valid access code in the X-Access-Code header.
    """
    if not access_code or not verify_access_code(access_code):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing access code",
            headers={"WWW-Authenticate": "APIKey"},
        )
    # In a real app, you would return a user object here.
    # For this simple case, we just proceed if the code is valid.
    return {"username": "authenticated_user"}
