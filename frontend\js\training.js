/**
 * ML Model Training Interface
 * Provides real-time monitoring and control of ML model training
 */

class TrainingController {
    constructor() {
        this.stationId = '61151080'; // Default station ID
        this.trainingInProgress = false;
        this.pollInterval = null;
        this.logs = [];
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadTrainingStatus();
        
        // Auto-refresh status every 30 seconds
        setInterval(() => {
            if (!this.trainingInProgress) {
                this.loadTrainingStatus();
            }
        }, 30000);
    }
    
    bindEvents() {
        document.getElementById('startTraining').addEventListener('click', () => {
            this.startTraining();
        });
        
        document.getElementById('refreshStatus').addEventListener('click', () => {
            this.loadTrainingStatus();
        });
    }
    
    async loadTrainingStatus() {
        try {
            this.addLog('INFO', 'Checking model training status...');
            
            const response = await fetch(`/api/ml/analytics/training-status/${this.stationId}`, {
                headers: {
                    'X-Access-Code': '1234'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const status = await response.json();
            this.updateModelStatus(status);
            
            this.addLog('INFO', `Status updated - ${status.total_models_loaded}/4 models loaded`);
            
        } catch (error) {
            this.addLog('ERROR', `Failed to load training status: ${error.message}`);
            console.error('Error loading training status:', error);
        }
    }
    
    updateModelStatus(status) {
        const models = status.models_loaded;
        
        // Update individual model status indicators
        this.updateModelIndicator('forecasterStatus', 'forecasterMetric', models.forecaster_model, 'Trained', 'Not Trained');
        this.updateModelIndicator('anomalyStatus', 'anomalyMetric', models.anomaly_model, 'Trained', 'Not Trained');
        this.updateModelIndicator('consumptionStatus', 'consumptionMetric', models.consumption_model, 'Trained', 'Not Trained');
        this.updateModelIndicator('maintenanceStatus', 'maintenanceMetric', models.maintenance_model, 'Trained', 'Not Trained');
        
        // Show saved model files info
        if (status.saved_model_files && Object.keys(status.saved_model_files).length > 0) {
            this.addLog('INFO', `Found ${Object.keys(status.saved_model_files).length} saved model files`);
        }
    }
    
    updateModelIndicator(statusId, metricId, isReady, readyText, notReadyText) {
        const statusEl = document.getElementById(statusId);
        const metricEl = document.getElementById(metricId);
        
        if (isReady) {
            statusEl.className = 'status-indicator status-ready';
            metricEl.textContent = readyText;
        } else {
            statusEl.className = 'status-indicator status-pending';
            metricEl.textContent = notReadyText;
        }
    }
    
    async startTraining() {
        if (this.trainingInProgress) {
            this.addLog('WARNING', 'Training already in progress');
            return;
        }
        
        const daysBack = parseInt(document.getElementById('daysBack').value) || 30;
        
        if (daysBack < 7 || daysBack > 365) {
            this.addLog('ERROR', 'Please select between 7 and 365 days of training data');
            return;
        }
        
        this.trainingInProgress = true;
        this.resetTrainingUI();
        
        try {
            this.addLog('INFO', `Starting ML model training with ${daysBack} days of historical data...`);
            
            // Disable training button
            const startButton = document.getElementById('startTraining');
            startButton.disabled = true;
            startButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Training...';
            
            // Start training
            const response = await fetch(`/api/ml/analytics/train/${this.stationId}?days_back=${daysBack}`, {
                method: 'POST',
                headers: {
                    'X-Access-Code': '1234',
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            this.handleTrainingResult(result);
            
        } catch (error) {
            this.addLog('ERROR', `Training failed: ${error.message}`);
            this.trainingInProgress = false;
            this.resetTrainingButton();
        }
    }
    
    handleTrainingResult(result) {
        this.trainingInProgress = false;
        this.resetTrainingButton();
        
        if (result.status === 'failed') {
            this.addLog('ERROR', `Training failed: ${result.error}`);
            this.updateProgress(0, 'Training failed');
            return;
        }
        
        const progress = result.training_progress;
        const summary = result.training_summary;
        
        // Update progress
        this.updateProgress(progress.progress_percent, progress.current_task);
        
        // Update stages
        this.updateTrainingStages(progress.stages_completed, progress.stage);
        
        // Show training logs
        this.addLog('SUCCESS', `Training completed in ${Math.round(summary.training_duration)} seconds`);
        this.addLog('INFO', `Processed ${summary.total_data_points} data points`);
        this.addLog('INFO', `Trained ${summary.models_trained} models with ${summary.success_rate}% success rate`);
        
        // Show metrics
        this.displayTrainingMetrics(result.model_metrics);
        
        // Add completion logs
        if (progress.stages_completed.includes('data_preparation')) {
            this.addLog('INFO', '✓ Data collection and preparation completed');
        }
        if (progress.stages_completed.includes('forecaster_training')) {
            this.addLog('INFO', '✓ Production forecaster training completed');
        }
        if (progress.stages_completed.includes('anomaly_training')) {
            this.addLog('INFO', '✓ Anomaly detection training completed');
        }
        if (progress.stages_completed.includes('consumption_training')) {
            this.addLog('INFO', '✓ Consumption analysis training completed');
        }
        if (progress.stages_completed.includes('maintenance_training')) {
            this.addLog('INFO', '✓ Maintenance prediction training completed');
        }
        if (progress.stages_completed.includes('saving_models')) {
            this.addLog('INFO', '✓ Models saved successfully');
        }
        
        // Refresh model status
        setTimeout(() => {
            this.loadTrainingStatus();
        }, 2000);
    }
    
    resetTrainingUI() {
        // Reset progress
        this.updateProgress(0, 'Initializing training...');
        
        // Reset all stages to pending
        const stages = ['data-collection', 'forecaster', 'anomaly', 'consumption', 'maintenance', 'saving'];
        stages.forEach(stage => {
            const stageEl = document.getElementById(`stage-${stage}`);
            if (stageEl) {
                stageEl.className = 'stage-item pending';
            }
        });
        
        // Clear previous metrics
        document.getElementById('trainingMetrics').style.display = 'none';
    }
    
    resetTrainingButton() {
        const startButton = document.getElementById('startTraining');
        startButton.disabled = false;
        startButton.innerHTML = '<i class="fas fa-play"></i> Start Training';
    }
    
    updateProgress(percent, task) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const currentTask = document.getElementById('currentTask');
        
        progressFill.style.width = `${percent}%`;
        progressText.textContent = `${percent}%`;
        currentTask.textContent = task;
    }
    
    updateTrainingStages(completedStages, currentStage) {
        const stageMapping = {
            'data_collection': 'data-collection',
            'data_preparation': 'data-collection',
            'forecaster_training': 'forecaster',
            'anomaly_training': 'anomaly',
            'consumption_training': 'consumption',
            'maintenance_training': 'maintenance',
            'saving_models': 'saving'
        };
        
        // Mark completed stages
        completedStages.forEach(stage => {
            const stageId = stageMapping[stage];
            if (stageId) {
                const stageEl = document.getElementById(`stage-${stageId}`);
                if (stageEl) {
                    stageEl.className = 'stage-item completed';
                }
            }
        });
        
        // Mark current active stage
        const currentStageId = stageMapping[currentStage];
        if (currentStageId) {
            const stageEl = document.getElementById(`stage-${currentStageId}`);
            if (stageEl && !completedStages.includes(currentStage)) {
                stageEl.className = 'stage-item active';
            }
        }
    }
    
    displayTrainingMetrics(metrics) {
        const metricsContainer = document.getElementById('metricsGrid');
        const trainingMetricsCard = document.getElementById('trainingMetrics');
        
        let metricsHTML = '';
        
        // Forecaster metrics
        if (metrics.forecaster && !metrics.forecaster.error) {
            metricsHTML += `
                <div class="metric-card">
                    <div class="metric-title">Forecaster Performance</div>
                    <div class="metric-value">${(metrics.forecaster.model_score * 100).toFixed(1)}%</div>
                    <div class="metric-description">Model accuracy score on training data</div>
                </div>
            `;
        }
        
        // Anomaly detector metrics
        if (metrics.anomaly_detector && !metrics.anomaly_detector.error) {
            metricsHTML += `
                <div class="metric-card">
                    <div class="metric-title">Anomaly Detection</div>
                    <div class="metric-value">${metrics.anomaly_detector.models_trained}</div>
                    <div class="metric-description">Models trained (Isolation Forest, SVM, Elliptic Envelope)</div>
                </div>
            `;
        }
        
        // Consumption analyzer metrics
        if (metrics.consumption_analyzer && !metrics.consumption_analyzer.error) {
            metricsHTML += `
                <div class="metric-card">
                    <div class="metric-title">Consumption Patterns</div>
                    <div class="metric-value">${metrics.consumption_analyzer.n_clusters || 'N/A'}</div>
                    <div class="metric-description">Consumption pattern clusters identified</div>
                </div>
            `;
        }
        
        // Maintenance model metrics
        if (metrics.maintenance_model && !metrics.maintenance_model.error) {
            metricsHTML += `
                <div class="metric-card">
                    <div class="metric-title">Maintenance Prediction</div>
                    <div class="metric-value">${(metrics.maintenance_model.cv_f1_score * 100).toFixed(1)}%</div>
                    <div class="metric-description">Cross-validation F1 score for degradation prediction</div>
                </div>
            `;
        }
        
        if (metricsHTML) {
            metricsContainer.innerHTML = metricsHTML;
            trainingMetricsCard.style.display = 'block';
        }
    }
    
    addLog(level, message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = {
            timestamp,
            level,
            message
        };
        
        this.logs.push(logEntry);
        
        // Keep only last 100 log entries
        if (this.logs.length > 100) {
            this.logs = this.logs.slice(-100);
        }
        
        this.updateLogsDisplay();
    }
    
    updateLogsDisplay() {
        const logsContainer = document.getElementById('logsContainer');
        
        const logsHTML = this.logs.map(log => {
            const levelClass = `log-level-${log.level.toLowerCase()}`;
            return `
                <div class="log-entry">
                    <span class="log-timestamp">[${log.timestamp}]</span>
                    <span class="${levelClass}">[${log.level}]</span>
                    <span>${log.message}</span>
                </div>
            `;
        }).join('');
        
        logsContainer.innerHTML = logsHTML;
        
        // Auto-scroll to bottom
        logsContainer.scrollTop = logsContainer.scrollHeight;
    }
}

// Initialize training controller when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.trainingController = new TrainingController();
});
