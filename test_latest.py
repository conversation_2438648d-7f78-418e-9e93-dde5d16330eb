import asyncio
from src.services.deye_service import DeyeAPIService

async def test_raw_latest():
    service = DeyeAPIService()
    try:
        token = await service.authenticate()
        print('Raw latest data from Deye API:')
        latest = await service.get_station_latest_data('61151080')
        print(f'Full response: {latest}')
        
        print(f'\nKey fields:')
        print(f'  batterySOC: {latest.get("batterySOC")}')
        print(f'  gridPower: {latest.get("gridPower")}') 
        print(f'  wirePower: {latest.get("wirePower")}')
        print(f'  consumptionPower: {latest.get("consumptionPower")}')
        print(f'  batteryPower: {latest.get("batteryPower")}')
        
    except Exception as e:
        print(f'Error: {e}')
    finally:
        await service.close_session()

if __name__ == "__main__":
    asyncio.run(test_raw_latest())
