<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar Monitor Dashboard</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-2.35.2.min.js"></script>
</head>
<body>
    <!-- Login Modal -->
    <div id="loginModal" class="modal active">
        <div class="modal-content">
            <div class="login-container">
                <div class="login-header">
                    <i class="fas fa-solar-panel"></i>
                    <h2>Solar Monitor</h2>
                    <p>Enter your access code to continue</p>
                </div>
                <form id="loginForm">
                    <div class="input-group">
                        <input type="password" id="accessCode" placeholder="Access Code" required>
                        <button type="submit" id="loginBtn">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                    </div>
                    <div id="loginError" class="error-message"></div>
                </form>
            </div>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard" class="dashboard hidden">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <div class="header-left">
                    <h1><i class="fas fa-solar-panel"></i> Solar Monitor</h1>
                    <div class="connection-status" id="connectionStatus">
                        <i class="fas fa-circle"></i>
                        <span>Connecting...</span>
                    </div>
                </div>
                <div class="header-right">
                    <button id="refreshBtn" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button id="logoutBtn" class="btn btn-danger">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            </div>
        </header>

        <!-- KPI Cards -->
        <section class="kpi-section">
            <div class="kpi-grid">
                <div class="kpi-card generation">
                    <div class="kpi-header">
                        <i class="fas fa-solar-panel"></i>
                        <h3>Solar Generation</h3>
                    </div>
                    <div class="kpi-value" id="generationPower">-- kW</div>
                    <div class="kpi-subtitle" id="generationDaily">Daily: -- kWh</div>
                </div>

                <div class="kpi-card battery">
                    <div class="kpi-header">
                        <i class="fas fa-battery-three-quarters"></i>
                        <h3>Battery</h3>
                    </div>
                    <div class="kpi-value" id="batterySoc">--%</div>
                    <div class="kpi-subtitle" id="batteryPower">-- kW</div>
                    <div class="battery-indicator">
                        <div class="battery-fill" id="batteryFill"></div>
                    </div>
                </div>

                <div class="kpi-card grid">
                    <div class="kpi-header">
                        <i class="fas fa-plug"></i>
                        <h3>Grid Power</h3>
                    </div>
                    <div class="kpi-value" id="gridPower">-- kW</div>
                    <div class="kpi-subtitle" id="gridStatus">--</div>
                </div>

                <div class="kpi-card consumption">
                    <div class="kpi-header">
                        <i class="fas fa-home"></i>
                        <h3>Consumption</h3>
                    </div>
                    <div class="kpi-value" id="consumptionPower">-- kW</div>
                    <div class="kpi-subtitle" id="consumptionDaily">Daily: -- kWh</div>
                </div>
            </div>
        </section>

        <!-- Charts Section -->
        <section class="charts-section">
            <div class="chart-container">
                <div class="chart-header">
                    <h3>Real-Time Power Flow</h3>
                    <div class="chart-controls">
                        <button class="btn btn-small" id="chart24h">24h</button>
                        <button class="btn btn-small active" id="chart7d">7d</button>
                        <button class="btn btn-small" id="chart30d">30d</button>
                    </div>
                </div>
                <div id="powerChart" class="chart"></div>
            </div>
        </section>

        <!-- AI Insights Section -->
        <section class="insights-section">
            <div class="insights-header">
                <h3><i class="fas fa-brain"></i> AI Insights</h3>
                <button class="btn btn-small" id="refreshInsights">
                    <i class="fas fa-sync"></i> Refresh
                </button>
            </div>
            
            <div class="insights-grid">
                <!-- Production Forecast -->
                <div class="insight-card forecast">
                    <div class="insight-header">
                        <i class="fas fa-chart-line"></i>
                        <h4>24h Forecast</h4>
                    </div>
                    <div class="insight-content" id="forecastContent">
                        <div class="forecast-summary">
                            <div class="forecast-metric">
                                <span class="label">Expected Generation:</span>
                                <span class="value" id="forecastGeneration">Loading...</span>
                            </div>
                            <div class="forecast-metric">
                                <span class="label">Peak Time:</span>
                                <span class="value" id="forecastPeakTime">--</span>
                            </div>
                            <div class="forecast-metric">
                                <span class="label">Confidence:</span>
                                <span class="value" id="forecastConfidence">--%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Health -->
                <div class="insight-card health">
                    <div class="insight-header">
                        <i class="fas fa-heartbeat"></i>
                        <h4>System Health</h4>
                    </div>
                    <div class="insight-content" id="healthContent">
                        <div class="health-status">
                            <div class="health-indicator" id="healthIndicator">
                                <span class="status-dot"></span>
                                <span id="healthStatus">Analyzing...</span>
                            </div>
                            <div class="anomaly-count">
                                <span>Anomalies (48h): </span>
                                <span id="anomalyCount">--</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance -->
                <div class="insight-card performance">
                    <div class="insight-header">
                        <i class="fas fa-tachometer-alt"></i>
                        <h4>Performance</h4>
                    </div>
                    <div class="insight-content" id="performanceContent">
                        <div class="performance-metrics">
                            <div class="metric">
                                <span class="label">Efficiency:</span>
                                <span class="value" id="systemEfficiency">--%</span>
                            </div>
                            <div class="metric">
                                <span class="label">Trend:</span>
                                <span class="value" id="performanceTrend">--</span>
                            </div>
                            <div class="metric">
                                <span class="label">Self-Sufficiency:</span>
                                <span class="value" id="selfSufficiency">--%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="insight-card recommendations">
                    <div class="insight-header">
                        <i class="fas fa-lightbulb"></i>
                        <h4>Recommendations</h4>
                    </div>
                    <div class="insight-content" id="recommendationsContent">
                        <div class="recommendations-list" id="recommendationsList">
                            <div class="recommendation-item">
                                <i class="fas fa-sync fa-spin"></i>
                                <span>Loading recommendations...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- System Status -->
        <section class="status-section">
            <div class="status-card">
                <div class="status-header">
                    <h3><i class="fas fa-info-circle"></i> System Status</h3>
                    <span class="status-indicator" id="systemStatus">Unknown</span>
                </div>
                <div class="status-details" id="statusDetails">
                    <p>Loading system information...</p>
                </div>
                <div class="last-update">
                    Last update: <span id="lastUpdate">--</span>
                </div>
            </div>
        </section>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="spinner"></div>
        <p>Loading data...</p>
    </div>

    <!-- Error Toast -->
    <div id="errorToast" class="toast error hidden">
        <i class="fas fa-exclamation-triangle"></i>
        <span id="errorMessage"></span>
        <button id="closeError">&times;</button>
    </div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
