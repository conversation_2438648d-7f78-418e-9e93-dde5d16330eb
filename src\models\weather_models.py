"""
Weather data models for Solar Display application

Pydantic models for weather data from Open-Meteo API and other sources.
Designed for solar energy forecasting in Villieria, Pretoria, South Africa.

Models include:
- Current weather conditions
- Weather forecasts for solar production planning
- Historical weather data for ML training
- Weather alerts and warnings
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum

class WeatherSource(str, Enum):
    """Weather data sources"""
    OPEN_METEO = "open-meteo"
    MANUAL = "manual"
    ESTIMATED = "estimated"

class WMOWeatherCode(int, Enum):
    """WMO Weather interpretation codes"""
    CLEAR_SKY = 0
    MAINLY_CLEAR = 1
    PARTLY_CLOUDY = 2
    OVERCAST = 3
    FOG = 45
    DEPOSITING_RIME_FOG = 48
    DRIZZLE_LIGHT = 51
    DRIZZLE_MODERATE = 53
    DRIZZLE_DENSE = 55
    FREEZING_DRIZZLE_LIGHT = 56
    FREEZING_DRIZZLE_DENSE = 57
    RAIN_SLIGHT = 61
    RAIN_MODERATE = 63
    RAIN_HEAVY = 65
    FREEZING_RAIN_LIGHT = 66
    FREEZING_RAIN_HEAVY = 67
    SNOW_FALL_SLIGHT = 71
    SNOW_FALL_MODERATE = 73
    SNOW_FALL_HEAVY = 75
    SNOW_GRAINS = 77
    RAIN_SHOWERS_SLIGHT = 80
    RAIN_SHOWERS_MODERATE = 81
    RAIN_SHOWERS_VIOLENT = 82
    SNOW_SHOWERS_SLIGHT = 85
    SNOW_SHOWERS_HEAVY = 86
    THUNDERSTORM = 95
    THUNDERSTORM_SLIGHT_HAIL = 96
    THUNDERSTORM_HEAVY_HAIL = 99

class WeatherCondition(BaseModel):
    """Current weather conditions for solar production analysis"""
    timestamp: datetime = Field(..., description="Weather observation time")
    temperature: float = Field(..., description="Air temperature (°C)")
    humidity: float = Field(..., ge=0, le=100, description="Relative humidity (%)")
    solar_radiation: float = Field(..., ge=0, description="Solar irradiance (W/m²)")
    cloud_cover: float = Field(..., ge=0, le=100, description="Cloud coverage (%)")
    wind_speed: float = Field(..., ge=0, description="Wind speed (m/s)")
    pressure: Optional[float] = Field(None, description="Atmospheric pressure (hPa)")
    weather_code: Optional[int] = Field(None, description="WMO weather code")
    visibility: Optional[float] = Field(None, description="Visibility (km)")
    uv_index: Optional[float] = Field(None, description="UV index")
    source: WeatherSource = Field(default=WeatherSource.OPEN_METEO, description="Data source")
    
    @property
    def weather_description(self) -> str:
        """Human-readable weather description"""
        code_descriptions = {
            0: "Clear sky",
            1: "Mainly clear",
            2: "Partly cloudy", 
            3: "Overcast",
            45: "Fog",
            48: "Depositing rime fog",
            51: "Light drizzle",
            53: "Moderate drizzle",
            55: "Dense drizzle",
            61: "Slight rain",
            63: "Moderate rain",
            65: "Heavy rain",
            71: "Slight snow fall",
            73: "Moderate snow fall",
            75: "Heavy snow fall",
            80: "Slight rain showers",
            81: "Moderate rain showers",
            82: "Violent rain showers",
            95: "Thunderstorm",
            96: "Thunderstorm with slight hail",
            99: "Thunderstorm with heavy hail"
        }
        
        if self.weather_code and self.weather_code in code_descriptions:
            return code_descriptions[self.weather_code]
        
        # Fallback based on cloud cover
        if self.cloud_cover <= 10:
            return "Clear sky"
        elif self.cloud_cover <= 30:
            return "Partly cloudy"
        elif self.cloud_cover <= 70:
            return "Cloudy"
        else:
            return "Overcast"
    
    @property
    def solar_production_index(self) -> float:
        """
        Solar production favorability index (0-1)
        1.0 = Perfect conditions, 0.0 = No production possible
        """
        # Base score from solar radiation
        radiation_score = min(self.solar_radiation / 1000, 1.0)
        
        # Cloud cover penalty
        cloud_penalty = (100 - self.cloud_cover) / 100
        
        # Temperature efficiency (panels work better when cooler)
        temp_efficiency = 1.0
        if self.temperature > 25:
            temp_efficiency = max(0.7, 1.0 - (self.temperature - 25) * 0.005)
        
        # Wind benefit (cooling effect)
        wind_benefit = min(1.0 + self.wind_speed * 0.01, 1.1)
        
        return radiation_score * cloud_penalty * temp_efficiency * wind_benefit

class WeatherForecast(BaseModel):
    """Weather forecast for solar production planning"""
    location: str = Field(default="Villieria, Pretoria", description="Forecast location")
    forecast_time: datetime = Field(..., description="When forecast was generated")
    valid_time: datetime = Field(..., description="Forecast valid time")
    conditions: WeatherCondition = Field(..., description="Predicted weather conditions")
    confidence: float = Field(default=0.8, ge=0, le=1, description="Forecast confidence")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class DailyWeatherSummary(BaseModel):
    """Daily weather summary for solar analytics"""
    date: str = Field(..., description="Date (YYYY-MM-DD)")
    temperature_max: float = Field(..., description="Maximum temperature (°C)")
    temperature_min: float = Field(..., description="Minimum temperature (°C)")
    temperature_avg: float = Field(..., description="Average temperature (°C)")
    solar_radiation_total: float = Field(..., description="Total daily solar radiation (Wh/m²)")
    solar_radiation_peak: float = Field(..., description="Peak solar radiation (W/m²)")
    sunshine_duration: float = Field(..., description="Sunshine duration (hours)")
    cloud_cover_avg: float = Field(..., description="Average cloud cover (%)")
    precipitation_total: float = Field(default=0.0, description="Total precipitation (mm)")
    wind_speed_max: float = Field(..., description="Maximum wind speed (m/s)")
    weather_code_dominant: Optional[int] = Field(None, description="Dominant weather code")
    
    @property
    def solar_production_potential(self) -> str:
        """Daily solar production potential rating"""
        if self.solar_radiation_total > 6000:  # > 6 kWh/m²
            return "Excellent"
        elif self.solar_radiation_total > 4500:  # > 4.5 kWh/m²
            return "Good"
        elif self.solar_radiation_total > 3000:  # > 3 kWh/m²
            return "Fair"
        else:
            return "Poor"

class WeatherAlert(BaseModel):
    """Weather alerts affecting solar production"""
    alert_type: str = Field(..., description="Type of alert")
    severity: str = Field(..., description="Severity level")
    title: str = Field(..., description="Alert title")
    description: str = Field(..., description="Alert description")
    start_time: datetime = Field(..., description="Alert start time")
    end_time: Optional[datetime] = Field(None, description="Alert end time")
    impact_on_solar: str = Field(..., description="Expected impact on solar production")
    recommendations: List[str] = Field(default_factory=list, description="Recommended actions")

class WeatherHistory(BaseModel):
    """Historical weather data for ML training"""
    station_id: str = Field(default="pretoria", description="Weather station ID")
    data_start: datetime = Field(..., description="Start of data period")
    data_end: datetime = Field(..., description="End of data period")
    total_records: int = Field(..., description="Number of data points")
    hourly_data: List[WeatherCondition] = Field(..., description="Hourly weather data")
    daily_summaries: List[DailyWeatherSummary] = Field(default_factory=list, description="Daily summaries")

class WeatherAPIResponse(BaseModel):
    """Standard response format for weather API endpoints"""
    success: bool = Field(..., description="Request success status")
    message: str = Field(default="OK", description="Response message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    source: WeatherSource = Field(default=WeatherSource.OPEN_METEO, description="Data source")

class CurrentWeatherResponse(WeatherAPIResponse):
    """Response for current weather endpoint"""
    data: WeatherCondition

class ForecastResponse(WeatherAPIResponse):
    """Response for weather forecast endpoint"""
    data: List[WeatherForecast]

class HistoricalWeatherResponse(WeatherAPIResponse):
    """Response for historical weather endpoint"""
    data: WeatherHistory

class SolarWeatherInsights(BaseModel):
    """Weather insights specific to solar energy production"""
    current_conditions: WeatherCondition = Field(..., description="Current weather")
    today_summary: DailyWeatherSummary = Field(..., description="Today's weather summary")
    production_forecast: List[float] = Field(..., description="Hourly production forecast (24h)")
    optimal_hours: List[int] = Field(..., description="Hours with best solar conditions")
    weather_efficiency_impact: float = Field(..., description="Weather impact on efficiency (%)")
    recommendations: List[str] = Field(default_factory=list, description="Weather-based recommendations")
    alerts: List[WeatherAlert] = Field(default_factory=list, description="Relevant weather alerts")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# =================== UTILITY FUNCTIONS ===================

def create_weather_condition_from_openmeteo(data: Dict[str, Any], timestamp: datetime) -> WeatherCondition:
    """
    Create WeatherCondition from Open-Meteo API response
    
    Args:
        data: Open-Meteo API data dictionary
        timestamp: Timestamp for the weather data
        
    Returns:
        WeatherCondition object
    """
    return WeatherCondition(
        timestamp=timestamp,
        temperature=data.get("temperature_2m", 20.0),
        humidity=data.get("relative_humidity_2m", 50.0),
        solar_radiation=data.get("shortwave_radiation", 0.0),
        cloud_cover=data.get("cloud_cover", 50.0),
        wind_speed=data.get("wind_speed_10m", 0.0) / 3.6,  # Convert km/h to m/s
        pressure=data.get("surface_pressure"),
        weather_code=data.get("weather_code"),
        source=WeatherSource.OPEN_METEO
    )

def categorize_solar_conditions(weather: WeatherCondition) -> str:
    """
    Categorize solar production conditions based on weather
    
    Args:
        weather: Current weather conditions
        
    Returns:
        Solar condition category
    """
    solar_index = weather.solar_production_index
    
    if solar_index >= 0.8:
        return "Excellent"
    elif solar_index >= 0.6:
        return "Good" 
    elif solar_index >= 0.4:
        return "Fair"
    elif solar_index >= 0.2:
        return "Poor"
    else:
        return "Very Poor"

def estimate_cloud_impact(cloud_cover: float) -> Dict[str, Any]:
    """
    Estimate the impact of cloud cover on solar production
    
    Args:
        cloud_cover: Cloud coverage percentage (0-100)
        
    Returns:
        Dictionary with impact analysis
    """
    # Cloud impact on solar production (approximate)
    if cloud_cover <= 10:
        reduction = 0
        description = "Clear skies - optimal solar production"
    elif cloud_cover <= 30:
        reduction = 15
        description = "Partly cloudy - minor reduction in production"
    elif cloud_cover <= 60:
        reduction = 35
        description = "Cloudy - moderate reduction in production"
    elif cloud_cover <= 80:
        reduction = 60
        description = "Overcast - significant reduction in production"
    else:
        reduction = 80
        description = "Heavy clouds - major reduction in production"
    
    return {
        "cloud_cover": cloud_cover,
        "production_reduction_pct": reduction,
        "description": description,
        "efficiency_factor": (100 - reduction) / 100
    }

def get_pretoria_weather_context() -> Dict[str, Any]:
    """
    Get location-specific weather context for Pretoria
    
    Returns:
        Dictionary with Pretoria weather characteristics
    """
    return {
        "location": "Villieria, Pretoria, South Africa",
        "coordinates": {"latitude": -25.749, "longitude": 28.231},
        "timezone": "Africa/Johannesburg",
        "elevation_m": 1373,
        "climate": "Subtropical highland",
        "solar_potential": {
            "annual_ghi_kwh_m2": 2100,  # Global Horizontal Irradiance
            "peak_sun_hours": 4.2,
            "optimal_tilt_angle": 26,  # Degrees (approximately equal to latitude)
            "seasonal_variation": "Moderate (winter vs summer)"
        },
        "seasonal_patterns": {
            "summer": {"months": [12, 1, 2], "avg_temp": 23, "rainfall": "High"},
            "autumn": {"months": [3, 4, 5], "avg_temp": 19, "rainfall": "Low"},
            "winter": {"months": [6, 7, 8], "avg_temp": 14, "rainfall": "Very Low"},
            "spring": {"months": [9, 10, 11], "avg_temp": 20, "rainfall": "Moderate"}
        }
    }
