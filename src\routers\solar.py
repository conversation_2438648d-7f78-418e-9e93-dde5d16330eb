from fastapi import APIRouter, Depends, HTTPException
from typing import List
from datetime import datetime, timedelta
import time

from src.services.deye_service import DeyeAPIService, APIError
from src.services.auth_service import get_current_user
from src.models.solar_models import SolarStation, RealTimeData, HistoricalData, HistoricalDataPoint

router = APIRouter()

# Dependency to get DeyeAPIService instance
async def get_deye_service():
    service = DeyeAPIService()
    try:
        yield service
    finally:
        await service.close_session()

@router.get("/stations", response_model=List[SolarStation])
async def get_stations(
    current_user: dict = Depends(get_current_user),
    deye_service: DeyeAPIService = Depends(get_deye_service)
):
    """Get list of all solar stations"""
    try:
        stations_raw = await deye_service.get_station_list()
        
        # Handle different response formats
        if isinstance(stations_raw, dict):
            # The actual list is under the 'stationList' key
            station_list = stations_raw.get('stationList', [])
        elif isinstance(stations_raw, list):
            station_list = stations_raw
        else:
            station_list = []
        
        stations = [
            SolarStation(
                id=str(s.get('id', s.get('stationId', ''))),
                name=s.get('name', s.get('stationName', 'Unknown')),
                capacity=s.get('installedCapacity', s.get('capacity', 0.0)),
                location=s.get('locationAddress', s.get('location', 'Unknown')),
                status=s.get('connectionStatus', s.get('status', 'Unknown')),
                last_update=datetime.fromtimestamp(s.get('lastUpdateTime', s.get('updateTime', time.time())))
            ) for s in station_list
        ]
        return stations
    except Exception as e:
        # Handle authentication errors gracefully
        if "Deye API credentials not configured" in str(e):
            raise HTTPException(
                status_code=503,
                detail="Deye API credentials not configured. Please update your .env file with real credentials from your Deye Cloud developer account."
            )
        elif "No stations found" in str(e):
            raise HTTPException(
                status_code=404,
                detail="No solar stations found. This could mean: 1) No stations are associated with your Deye account, 2) The stations are not shared with your API app, or 3) There's a temporary issue with the Deye API."
            )
        else:
            raise HTTPException(status_code=500, detail=f"Deye API error: {str(e)}")

@router.get("/stations/{station_id}/latest")
async def get_station_latest(
    station_id: str, 
    current_user: dict = Depends(get_current_user),
    deye_service: DeyeAPIService = Depends(get_deye_service)
):
    """Get real-time data for a station"""
    try:
        latest_data = await deye_service.get_station_latest_data(station_id)
        
        # Return camelCase field names for frontend compatibility
        return {
            "stationId": station_id,
            "timestamp": datetime.fromtimestamp(latest_data.get('lastUpdateTime', time.time())).isoformat(),
            "generationPower": latest_data.get('generationPower', 0.0) or 0.0,
            "batterySoc": latest_data.get('batterySOC', latest_data.get('batterySoc', 0.0)) or 0.0,
            "batteryPower": latest_data.get('batteryPower', 0.0) or 0.0,
            "gridPower": latest_data.get('wirePower', 0.0) or 0.0,
            "consumptionPower": latest_data.get('consumptionPower', 0.0) or 0.0,
            "irradiateIntensity": latest_data.get('irradiateIntensity')
        }
    except Exception as e:
        # Handle authentication errors gracefully
        if "Deye API credentials not configured" in str(e):
            raise HTTPException(
                status_code=503,
                detail="Deye API credentials not configured. Please update your .env file with real credentials from your Deye Cloud developer account."
            )
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stations/{station_id}/history", response_model=HistoricalData)
async def get_station_history(
    station_id: str, 
    days: int = 7, 
    current_user: dict = Depends(get_current_user),
    deye_service: DeyeAPIService = Depends(get_deye_service)
):
    """Get historical data for a station for the last N days"""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        history_data = await deye_service.get_station_history_data(
            station_id,
            start_date.strftime("%Y-%m-%d"),
            end_date.strftime("%Y-%m-%d")
        )
        
        points = []
        for item in history_data.get('stationDataItems', []):
            try:
                # Safely extract date components
                year = item.get('year', 2025)
                month = item.get('month', 1)
                day = item.get('day', 1)
                
                # Ensure they are integers
                year = int(year) if year is not None else 2025
                month = int(month) if month is not None else 1
                day = int(day) if day is not None else 1
                
                point = HistoricalDataPoint(
                    date=f"{year:04d}-{month:02d}-{day:02d}",
                    generation_value=float(item.get('generationValue', 0.0) or 0.0),
                    consumption_value=float(item.get('consumptionValue', 0.0) or 0.0),
                    grid_value=float(item.get('gridValue', 0.0) or 0.0),
                    battery_charge=float(item.get('chargeValue', 0.0) or 0.0),
                    battery_discharge=float(item.get('dischargeValue', 0.0) or 0.0)
                )
                points.append(point)
            except Exception as item_error:
                # Skip invalid items but continue processing
                print(f"Error processing history item {item}: {item_error}")
                continue
        
        return HistoricalData(station_id=station_id, data=points)
    except Exception as e:
        # Handle authentication errors gracefully
        if "Deye API credentials not configured" in str(e):
            raise HTTPException(
                status_code=503,
                detail="Deye API credentials not configured. Please update your .env file with real credentials from your Deye Cloud developer account."
            )
        raise HTTPException(status_code=500, detail=str(e))
