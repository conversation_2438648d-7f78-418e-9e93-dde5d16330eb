"""
Database models for Solar Display application

Defines Pydantic models and SQLite table schemas for:
- Solar production data from Deye inverter
- Weather data from Open-Meteo API  
- ML training features
- Performance anomalies

All models are optimized for time-series data storage and analysis
for the Villieria, Pretoria solar installation.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum

class Season(str, Enum):
    """South African seasons for ML feature engineering"""
    SUMMER = "Summer"  # Dec-Feb
    AUTUMN = "Autumn"  # Mar-May  
    WINTER = "Winter"  # Jun-Aug
    SPRING = "Spring"  # Sep-Nov

class TimeOfDay(str, Enum):
    """Solar production time periods"""
    MORNING = "Morning"    # 06:00-10:00
    MIDDAY = "Midday"      # 10:00-14:00  
    AFTERNOON = "Afternoon" # 14:00-18:00
    EVENING = "Evening"    # 18:00-22:00
    NIGHT = "Night"        # 22:00-06:00

class WeatherCategory(str, Enum):
    """Weather condition categories for ML"""
    CLEAR = "Clear"
    PARTLY_CLOUDY = "Partly_Cloudy"
    CLOUDY = "Cloudy"
    OVERCAST = "Overcast"
    RAINY = "Rainy"

# =================== DATABASE MODELS ===================

class SolarProductionData(BaseModel):
    """Solar production data from Deye inverter API"""
    id: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    station_id: str = Field(..., description="Deye station identifier")
    total_power: float = Field(..., description="Total system power (kW)")
    daily_energy: float = Field(..., description="Daily energy production (kWh)")
    inverter_temp: Optional[float] = Field(None, description="Inverter temperature (°C)")
    dc_voltage: Optional[float] = Field(None, description="DC input voltage (V)")
    ac_voltage: Optional[float] = Field(None, description="AC output voltage (V)")
    efficiency: Optional[float] = Field(None, description="System efficiency (%)")
    grid_frequency: Optional[float] = Field(None, description="Grid frequency (Hz)")
    battery_soc: Optional[float] = Field(None, description="Battery state of charge (%)")
    battery_power: Optional[float] = Field(None, description="Battery charge/discharge (kW)")
    grid_power: Optional[float] = Field(None, description="Grid import/export (kW)")
    consumption_power: Optional[float] = Field(None, description="Current consumption (kW)")
    irradiate_intensity: Optional[float] = Field(None, description="Solar irradiation (W/m²)")
    error_code: Optional[str] = Field(None, description="Any error codes")
    raw_data: Optional[str] = Field(None, description="Complete JSON from Deye API")

class WeatherData(BaseModel):
    """Weather data from Open-Meteo API for Pretoria coordinates"""
    id: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    temperature: float = Field(..., description="Air temperature (°C)")
    humidity: float = Field(..., description="Relative humidity (%)")
    solar_radiation: float = Field(..., description="Solar irradiance (W/m²)")
    cloud_cover: float = Field(..., description="Cloud coverage (%)")
    wind_speed: float = Field(..., description="Wind speed (m/s)")
    pressure: Optional[float] = Field(None, description="Atmospheric pressure (hPa)")
    weather_code: Optional[int] = Field(None, description="WMO weather code")
    source: str = Field(default="open-meteo", description="Weather data source")

class MLFeatures(BaseModel):
    """Engineered features for ML model training"""
    id: Optional[int] = None
    timestamp: datetime = Field(..., description="Feature timestamp")
    production_kw: float = Field(..., description="Solar production (kW)")
    irradiance_wm2: float = Field(..., description="Solar irradiance (W/m²)")
    temperature_c: float = Field(..., description="Air temperature (°C)")
    cloud_cover_pct: float = Field(..., description="Cloud coverage (%)")
    efficiency_pct: float = Field(..., description="System efficiency (%)")
    season: Season = Field(..., description="South African season")
    time_of_day: TimeOfDay = Field(..., description="Time period")
    weather_category: WeatherCategory = Field(..., description="Weather condition")
    hour_sin: float = Field(..., description="Cyclical hour encoding (sin)")
    hour_cos: float = Field(..., description="Cyclical hour encoding (cos)")
    day_of_year_sin: float = Field(..., description="Cyclical day encoding (sin)")
    day_of_year_cos: float = Field(..., description="Cyclical day encoding (cos)")

# =================== API RESPONSE MODELS ===================

class SolarDataResponse(BaseModel):
    """Response model for solar production data API"""
    success: bool
    data: List[SolarProductionData]
    total_records: int
    timestamp: datetime = Field(default_factory=datetime.now)

class WeatherDataResponse(BaseModel):
    """Response model for weather data API"""
    success: bool
    data: List[WeatherData]
    total_records: int
    timestamp: datetime = Field(default_factory=datetime.now)

class MLInsights(BaseModel):
    """AI-powered solar system insights"""
    daily_forecast_kwh: float = Field(..., description="Predicted daily generation (kWh)")
    efficiency_rating: str = Field(..., description="Current efficiency rating")
    performance_trend: str = Field(..., description="Performance trend (improving/stable/declining)")
    recommendations: List[str] = Field(..., description="Actionable recommendations")
    confidence_score: float = Field(..., description="Overall prediction confidence")
    last_updated: datetime = Field(default_factory=datetime.now)

# =================== UTILITY FUNCTIONS ===================

def get_season_from_date(date: datetime) -> Season:
    """
    Determine South African season from date
    
    Args:
        date: Date to analyze
        
    Returns:
        Season enum value
    """
    month = date.month
    if month in [12, 1, 2]:
        return Season.SUMMER
    elif month in [3, 4, 5]:
        return Season.AUTUMN
    elif month in [6, 7, 8]:
        return Season.WINTER
    else:  # 9, 10, 11
        return Season.SPRING

def get_time_of_day(time: datetime) -> TimeOfDay:
    """
    Categorize time into solar production periods
    
    Args:
        time: Time to categorize
        
    Returns:
        TimeOfDay enum value
    """
    hour = time.hour
    if 6 <= hour < 10:
        return TimeOfDay.MORNING
    elif 10 <= hour < 14:
        return TimeOfDay.MIDDAY
    elif 14 <= hour < 18:
        return TimeOfDay.AFTERNOON
    elif 18 <= hour < 22:
        return TimeOfDay.EVENING
    else:
        return TimeOfDay.NIGHT

def categorize_weather(cloud_cover: float, weather_code: Optional[int] = None) -> WeatherCategory:
    """
    Categorize weather conditions for ML features
    
    Args:
        cloud_cover: Cloud coverage percentage (0-100)
        weather_code: WMO weather code (optional)
        
    Returns:
        WeatherCategory enum value
    """
    # Check for rain first (WMO codes 51-67, 80-82, 95-99)
    if weather_code and weather_code in range(51, 68) or weather_code in range(80, 83) or weather_code in range(95, 100):
        return WeatherCategory.RAINY
    
    # Categorize by cloud cover
    if cloud_cover <= 10:
        return WeatherCategory.CLEAR
    elif cloud_cover <= 30:
        return WeatherCategory.PARTLY_CLOUDY
    elif cloud_cover <= 70:
        return WeatherCategory.CLOUDY
    else:
        return WeatherCategory.OVERCAST
