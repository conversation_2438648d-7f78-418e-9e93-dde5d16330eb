#!/usr/bin/env python3
"""
Simple test for data collection service
"""
import asyncio
from src.services.data_collection_service import DataCollectionService
from src.database.migrations import get_database_stats

async def test_data_collection():
    """Test the data collection pipeline"""
    print("🔋 Testing Data Collection Pipeline...")
    
    try:
        # Initialize service
        service = DataCollectionService()
        await service.initialize()
        print("✅ Service initialized")
        
        # Test weather data collection
        await service.collect_weather_data()
        print("✅ Weather data collected")
        
        # Check database stats
        stats = await get_database_stats()
        print(f"📊 Database stats: {stats}")
        
        print("🎉 Data collection test successful!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_data_collection())
