# Deye Solar Inverter API Analysis

## Executive Summary

The Deye Cloud API provides comprehensive access to solar inverter data through RESTful endpoints. Based on extensive research of the official documentation and sample code, this analysis covers authentication, data retrieval capabilities, security considerations, and implementation recommendations for read-only solar monitoring applications.

## API Architecture

### Base URL and Versioning
- **Production URL**: `https://eu1-developer.deyecloud.com/v1.0/`
- **API Version**: v1.0 (current stable version)
- **Protocol**: HTTPS (TLS encrypted)
- **Authentication**: Bearer token-based

### Authentication Flow

The Deye API uses a multi-step authentication process:

1. **Application Registration**: Obtain `APP_ID` and `APP_SECRET` from developer portal
2. **Token Request**: Exchange credentials for access token
3. **API Access**: Use Bearer token for all subsequent requests

#### Token Acquisition
```http
POST /v1.0/account/token?appId={APP_ID}
Content-Type: application/json

{
  "appSecret": "your_app_secret",
  "email": "<EMAIL>", 
  "password": "sha256_hashed_password"
}
```

**Security Notes:**
- Password must be SHA-256 hashed before transmission
- Tokens expire (typically 5183999 seconds ≈ 60 days)
- Refresh tokens available for seamless renewal

### Core Read-Only Endpoints for Solar Monitoring

#### 1. Station Management
- **`GET /v1.0/station/list`**: Retrieve all solar stations
- **`GET /v1.0/station/latest`**: Real-time station data
- **`GET /v1.0/station/history`**: Historical station data
- **`GET /v1.0/station/device`**: Devices per station

#### 2. Device Operations  
- **`GET /v1.0/device/latest`**: Real-time device readings
- **`GET /v1.0/device/history`**: Historical device data
- **`GET /v1.0/device/measurePoints`**: Available measurement points
- **`GET /v1.0/device/alertList`**: Device alerts and warnings

#### 3. Configuration Reading
- **`GET /v1.0/config/system`**: System work mode parameters
- **`GET /v1.0/config/battery`**: Battery settings and status
- **`GET /v1.0/config/tou`**: Time-of-use configuration

## Key Data Points for Solar Monitoring

### Real-Time Metrics
- **Generation Power**: Current solar production (kW)
- **Battery SOC**: State of charge percentage
- **Battery Power**: Charge/discharge rate (kW) 
- **Grid Power**: Grid import/export (kW)
- **Consumption Power**: Current load consumption (kW)
- **Irradiate Intensity**: Solar irradiation level

### Historical Data Points
- **Generation Value**: Daily/monthly energy production (kWh)
- **Consumption Value**: Energy consumed (kWh)
- **Grid Value**: Net grid energy exchange (kWh)
- **Charge/Discharge Value**: Battery energy flow (kWh)
- **Full Power Hours**: Peak production duration

### System Status Indicators
- **Device State**: Online/Offline/Alert status
- **Connection Status**: Network connectivity
- **Alert Codes**: System warnings and faults
- **Last Update Time**: Data freshness timestamp

## Data Granularity Options

### Temporal Granularity
1. **Frame Level** (`granularity=1`): Minute-by-minute data for single day
2. **Daily** (`granularity=2`): Day-by-day aggregates (up to 31 days)
3. **Monthly** (`granularity=3`): Month-by-month data (up to 12 months)  
4. **Yearly** (`granularity=4`): Annual summaries

### Data Formats
- **JSON**: Primary response format
- **Timestamps**: Unix timestamps (seconds) for most endpoints
- **Units**: Standardized (kW for power, kWh for energy, % for SOC)

## Rate Limits and Best Practices

### API Constraints
- **Rate Limiting**: Not explicitly documented but recommended 1 req/sec
- **Data Retention**: Historical data available varies by granularity
- **Batch Processing**: Some endpoints support batch requests (up to 10 items)

### Recommended Polling Intervals
- **Real-time Dashboard**: 30-60 seconds for latest data
- **Historical Updates**: Daily for daily aggregates
- **Alert Monitoring**: 5-10 minutes for alert status
- **Configuration**: Only when needed (rarely changes)

## Error Handling

### Standard HTTP Status Codes
- **200**: Success with data payload
- **400**: Bad Request (invalid parameters)
- **401**: Unauthorized (invalid/expired token)
- **403**: Forbidden (insufficient permissions)
- **404**: Resource not found
- **500**: Internal server error

### API Response Structure
```json
{
  "success": true,
  "code": "1000000",
  "msg": "success", 
  "requestId": "unique_request_id",
  "data": {
    // Actual response data
  }
}
```

## Security Considerations

### For Read-Only Applications
1. **Token Storage**: Securely store access tokens (environment variables)
2. **HTTPS Only**: All communications must use TLS
3. **Credential Rotation**: Regular password updates recommended
4. **Error Logging**: Log failed requests for monitoring
5. **Input Validation**: Validate all API responses before processing

### Critical Safety Measures
- **No Write Operations**: Avoid all endpoints that modify inverter settings
- **Read-Only Access**: Use only GET requests and read-only POST endpoints
- **Data Validation**: Verify all retrieved data before storage/display
- **Fallback Handling**: Graceful degradation when API unavailable

## Implementation Recommendations

### Authentication Management
```python
class DeyeAuthManager:
    def __init__(self, app_id, app_secret, email, password):
        self.app_id = app_id
        self.app_secret = app_secret
        self.email = email
        self.password_hash = hashlib.sha256(password.encode()).hexdigest()
        self.token = None
        self.token_expiry = None
    
    def get_valid_token(self):
        if self.token and self.token_expiry > time.time():
            return self.token
        return self.refresh_token()
```

### Data Retrieval Strategy
1. **Station Discovery**: Get all stations on startup
2. **Device Enumeration**: Map devices per station
3. **Real-time Loop**: Continuous latest data polling
4. **Historical Sync**: Periodic historical data updates
5. **Alert Monitoring**: Regular alert checking

### Caching and Storage
- **In-Memory Cache**: Latest readings for real-time display
- **Time-Series Database**: Historical data storage (InfluxDB/TimescaleDB)
- **Configuration Cache**: Rarely-changing system settings
- **Error Resilience**: Local caching for API outages

## Sample Integration Code

### Basic Station Data Retrieval
```python
def get_solar_overview(token, station_id):
    """Get comprehensive solar system overview"""
    
    # Real-time data
    latest_url = f"{BASE_URL}/station/latest"
    latest_data = requests.post(latest_url, 
        headers={"Authorization": f"Bearer {token}"},
        json={"stationId": station_id}
    ).json()
    
    # Device list
    device_url = f"{BASE_URL}/station/device" 
    device_data = requests.post(device_url,
        headers={"Authorization": f"Bearer {token}"},
        json={"stationIds": [station_id]}
    ).json()
    
    return {
        "realtime": latest_data,
        "devices": device_data,
        "timestamp": datetime.now().isoformat()
    }
```

### Historical Data Sync
```python
def sync_daily_history(token, station_id, days_back=7):
    """Sync recent daily historical data"""
    
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days_back)
    
    history_url = f"{BASE_URL}/station/history"
    response = requests.post(history_url,
        headers={"Authorization": f"Bearer {token}"},
        json={
            "stationId": station_id,
            "granularity": 2,  # Daily
            "startAt": start_date.strftime("%Y-%m-%d"),
            "endAt": end_date.strftime("%Y-%m-%d")
        }
    )
    
    return response.json()["stationDataItems"]
```

## Conclusion

The Deye Cloud API provides robust, well-documented access to solar inverter data suitable for building comprehensive monitoring applications. The read-only endpoints offer sufficient granularity for both real-time dashboards and historical analysis while maintaining system security through proper authentication and error handling.

**Key Strengths:**
- Comprehensive data coverage (real-time + historical)
- Multiple granularity options
- Proper authentication and security
- Good error handling and status reporting
- Batch request support for efficiency

**Considerations:**
- Token management complexity
- Rate limiting (though not strictly enforced)
- Historical data retention limits
- Network dependency for real-time features

**Recommended for:** Building read-only solar monitoring dashboards, energy analytics applications, and automated reporting systems.
