import asyncio
from src.services.deye_service import DeyeAPIService
from src.models.solar_models import HistoricalDataPoint
from datetime import datetime, timedelta

async def test_history_parsing():
    service = DeyeAPIService()
    try:
        token = await service.authenticate()
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        history = await service.get_station_history_data(
            '61151080',
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )
        
        # Test creating one point
        item = history['stationDataItems'][0]
        print(f'Sample item keys: {list(item.keys())}')
        print(f'Year: {item.get("year")}, Month: {item.get("month")}, Day: {item.get("day")}')
        
        year = item.get('year', 2025)
        month = item.get('month', 1) 
        day = item.get('day', 1)
        
        date_str = f'{year:04d}-{month:02d}-{day:02d}'
        print(f'Generated date: {date_str}')
        
        point = HistoricalDataPoint(
            date=date_str,
            generation_value=float(item.get('generationValue', 0.0) or 0.0),
            consumption_value=float(item.get('consumptionValue', 0.0) or 0.0),
            grid_value=float(item.get('gridValue', 0.0) or 0.0),
            battery_charge=float(item.get('chargeValue', 0.0) or 0.0),
            battery_discharge=float(item.get('dischargeValue', 0.0) or 0.0)
        )
        print(f'Created point successfully: {point.date}')
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
    finally:
        await service.close_session()

if __name__ == "__main__":
    asyncio.run(test_history_parsing())
