import asyncio
import aiohttp
import hashlib
import time
from typing import List, Optional, Dict, Any

from src.config import settings

class APIError(Exception):
    pass

class AuthenticationError(APIError):
    pass

class DeyeAPIService:
    def __init__(self):
        self.base_url = settings.deye_base_url
        self.app_id = settings.deye_app_id
        self.app_secret = settings.deye_app_secret
        self.email = settings.deye_email
        self.password_hash = hashlib.sha256(settings.deye_password.encode()).hexdigest()
        self.token: Optional[str] = None
        self.token_expiry: Optional[float] = None
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Check if configuration is valid
        if not settings.is_deye_config_valid():
            raise AuthenticationError(
                "Deye API credentials not configured. Please update your .env file with real credentials from your Deye Cloud developer account."
            )

    async def _get_session(self) -> aiohttp.ClientSession:
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session

    async def close_session(self):
        if self.session and not self.session.closed:
            await self.session.close()

    async def authenticate(self) -> str:
        """Authenticate and get access token"""
        if self.token and self.token_expiry and self.token_expiry > time.time():
            return self.token

        auth_data = {
            "appSecret": self.app_secret,
            "email": self.email,
            "password": self.password_hash
        }
        
        session = await self._get_session()
        
        try:
            async with session.post(
                f"{self.base_url}/account/token",
                params={"appId": self.app_id},
                json=auth_data
            ) as response:
                response.raise_for_status()
                result = await response.json()
                
                if result.get("success"):
                    self.token = result["accessToken"]
                    # Deye gives expiresIn in seconds
                    self.token_expiry = time.time() + int(result["expiresIn"])
                    return self.token
                else:
                    raise AuthenticationError(result.get("msg", "Unknown authentication error"))
        except aiohttp.ClientError as e:
            raise APIError(f"Failed to connect to Deye API: {e}")


    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        token = await self.authenticate()
        headers = {"Authorization": f"Bearer {token}"}
        
        session = await self._get_session()
        
        try:
            async with session.request(method, f"{self.base_url}/{endpoint}", headers=headers, **kwargs) as response:
                result = await response.json()
                
                # Handle different response status codes
                if response.status == 500:
                    error_msg = result.get("msg", "Internal server error from Deye API")
                    if "station" in endpoint:
                        raise APIError(f"No stations found or Deye API error: {error_msg}")
                    else:
                        raise APIError(f"Deye API error for {endpoint}: {error_msg}")
                
                response.raise_for_status()
                
                if not result.get("success"):
                    raise APIError(result.get("msg", f"API error for {endpoint}"))
                    
                return result.get("data", result)
        except aiohttp.ClientError as e:
            raise APIError(f"API request failed for {endpoint}: {e}")

    async def get_station_list(self) -> List[Dict[str, Any]]:
        """Retrieve all solar stations"""
        return await self._make_request("post", "station/list", json={})

    async def get_station_latest_data(self, station_id: str) -> Dict[str, Any]:
        """Get real-time station data"""
        return await self._make_request("post", "station/latest", json={"stationId": station_id})

    async def get_station_history_data(self, station_id: str, start_date: str, end_date: str, granularity: int = 2) -> Dict[str, Any]:
        """Get historical station data"""
        return await self._make_request("post", "station/history", json={
            "stationId": station_id,
            "granularity": granularity,
            "startAt": start_date,
            "endAt": end_date
        })
