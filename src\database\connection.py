"""
Database infrastructure for Solar Display application

This module provides SQLite database connection and session management
for the Solar Analytics platform in Villieria, Pretoria, South Africa.

Key features:
- Async SQLite operations using aiosqlite
- Time-series optimized schema for solar production data
- Weather data caching for Open-Meteo API
- ML feature storage for training pipelines
- Anomaly detection tracking
"""

import aiosqlite
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

from src.config import settings

logger = logging.getLogger(__name__)

# Database file path
DB_PATH = Path("solar_display.db")

class DatabaseConnection:
    """
    Async SQLite database connection manager for Solar Display
    
    Provides context managers and connection pooling for efficient
    database operations in the solar analytics pipeline.
    """
    
    def __init__(self, db_path: Path = DB_PATH):
        self.db_path = db_path
        self._connection: Optional[aiosqlite.Connection] = None
        
    @asynccontextmanager
    async def get_connection(self):
        """
        Async context manager for database connections
        
        Usage:
            async with db.get_connection() as conn:
                await conn.execute("SELECT * FROM solar_production")
        """
        try:
            conn = await aiosqlite.connect(self.db_path)
            # Enable row factory for dict-like access
            conn.row_factory = aiosqlite.Row
            # Enable foreign keys
            await conn.execute("PRAGMA foreign_keys = ON")
            # Enable WAL mode for better concurrent performance
            await conn.execute("PRAGMA journal_mode = WAL")
            yield conn
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            raise
        finally:
            if conn:
                await conn.close()
    
    async def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        Execute a SELECT query and return results as list of dicts
        
        Args:
            query: SQL SELECT statement
            params: Query parameters for parameterized queries
            
        Returns:
            List of dictionaries with query results
        """
        async with self.get_connection() as conn:
            async with conn.execute(query, params) as cursor:
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]
    
    async def execute_insert(self, query: str, params: tuple = ()) -> int:
        """
        Execute an INSERT statement and return the last row ID
        
        Args:
            query: SQL INSERT statement
            params: Query parameters
            
        Returns:
            Last inserted row ID
        """
        async with self.get_connection() as conn:
            cursor = await conn.execute(query, params)
            await conn.commit()
            return cursor.lastrowid
    
    async def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        Execute UPDATE/DELETE statement and return affected rows
        
        Args:
            query: SQL UPDATE/DELETE statement
            params: Query parameters
            
        Returns:
            Number of affected rows
        """
        async with self.get_connection() as conn:
            await conn.execute(query, params)
            await conn.commit()
            return conn.total_changes

# Global database instance
db = DatabaseConnection()

async def get_db_connection():
    """Dependency injection for database connection"""
    return db
