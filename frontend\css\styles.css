/* Solar Monitor Dashboard Styles */
:root {
    --primary-color: #2E86AB;
    --secondary-color: #A23B72;
    --success-color: #F18F01;
    --warning-color: #C73E1D;
    --solar-yellow: #FFD23F;
    --battery-green: #06D6A0;
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-card: #3a3a3a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: #4a4a4a;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    --border-radius: 12px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    max-width: 400px;
    width: 90%;
}

.login-container {
    text-align: center;
}

.login-header {
    margin-bottom: 2rem;
}

.login-header i {
    font-size: 3rem;
    color: var(--solar-yellow);
    margin-bottom: 1rem;
}

.login-header h2 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.login-header p {
    color: var(--text-secondary);
}

.input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
}

input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn.btn-danger {
    background: var(--warning-color);
    color: white;
}

.btn.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn.active {
    background: var(--primary-color);
    color: white;
}

/* Dashboard Styles */
.dashboard {
    min-height: 100vh;
    padding: 1rem;
}

.dashboard.hidden {
    display: none;
}

.dashboard-header {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
}

.header-left h1 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.header-left h1 i {
    color: var(--solar-yellow);
    margin-right: 0.5rem;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.connection-status i {
    font-size: 0.5rem;
}

.connection-status.connected i {
    color: var(--battery-green);
}

.connection-status.disconnected i {
    color: var(--warning-color);
}

.header-right {
    display: flex;
    gap: 1rem;
}

/* KPI Cards */
.kpi-section {
    margin-bottom: 2rem;
}

.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.kpi-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

.kpi-card:hover {
    transform: translateY(-4px);
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
}

.kpi-card.generation::before {
    background: var(--solar-yellow);
}

.kpi-card.battery::before {
    background: var(--battery-green);
}

.kpi-card.grid::before {
    background: var(--primary-color);
}

.kpi-card.consumption::before {
    background: var(--secondary-color);
}

.kpi-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.kpi-header i {
    font-size: 1.5rem;
    color: var(--solar-yellow);
}

.kpi-card.battery .kpi-header i {
    color: var(--battery-green);
}

.kpi-card.grid .kpi-header i {
    color: var(--primary-color);
}

.kpi-card.consumption .kpi-header i {
    color: var(--secondary-color);
}

.kpi-header h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
}

.kpi-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.kpi-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.battery-indicator {
    margin-top: 1rem;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.battery-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--warning-color), var(--solar-yellow), var(--battery-green));
    border-radius: 4px;
    transition: width 0.5s ease;
    width: 0%;
}

/* Charts Section */
.charts-section {
    margin-bottom: 2rem;
}

.chart-container {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    color: var(--text-primary);
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart {
    height: 400px;
    width: 100%;
}

/* Status Section */
.status-section {
    margin-bottom: 2rem;
}

.status-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.status-header h3 {
    color: var(--text-primary);
}

.status-indicator {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator.normal {
    background: var(--battery-green);
    color: white;
}

.status-indicator.warning {
    background: var(--solar-yellow);
    color: var(--bg-primary);
}

.status-indicator.error {
    background: var(--warning-color);
    color: white;
}

.status-details {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.last-update {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Loading and Error States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;
}

.loading-overlay.hidden {
    display: none;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--solar-yellow);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-card);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 1001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.toast:not(.hidden) {
    transform: translateX(0);
}

.toast.error {
    border-left: 4px solid var(--warning-color);
}

.toast button {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
}

.error-message {
    color: var(--warning-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.hidden {
    display: none !important;
}

/* ===== AI INSIGHTS SECTION ===== */
.insights-section {
    padding: 1rem;
    margin-bottom: 2rem;
}

.insights-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 0 0.5rem;
}

.insights-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.insights-header h3 i {
    color: var(--primary-color);
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.insight-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.insight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
    border-color: var(--primary-color);
}

.insight-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.insight-header h4 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.insight-header i {
    font-size: 1.2rem;
}

.insight-card.forecast .insight-header i {
    color: var(--solar-yellow);
}

.insight-card.health .insight-header i {
    color: var(--battery-green);
}

.insight-card.performance .insight-header i {
    color: var(--primary-color);
}

.insight-card.recommendations .insight-header i {
    color: var(--success-color);
}

.insight-content {
    color: var(--text-secondary);
}

/* Forecast Card */
.forecast-summary {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.forecast-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.forecast-metric .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.forecast-metric .value {
    font-weight: 600;
    color: var(--text-primary);
}

/* Health Card */
.health-status {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.health-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--battery-green);
    animation: pulse 2s infinite;
}

.status-dot.warning {
    background: var(--warning-color);
}

.status-dot.error {
    background: var(--secondary-color);
}

.anomaly-count {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

/* Performance Card */
.performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.performance-metrics .metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.performance-metrics .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.performance-metrics .value {
    font-weight: 600;
    color: var(--text-primary);
}

/* Recommendations Card */
.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border-left: 3px solid var(--success-color);
}

.recommendation-item.warning {
    border-left-color: var(--warning-color);
}

.recommendation-item.urgent {
    border-left-color: var(--secondary-color);
}

.recommendation-item i {
    color: var(--success-color);
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.recommendation-item.warning i {
    color: var(--warning-color);
}

.recommendation-item.urgent i {
    color: var(--secondary-color);
}

/* Pulse animation for status indicators */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(6, 214, 160, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(6, 214, 160, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(6, 214, 160, 0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-right {
        justify-content: center;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
    }

    .chart-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .chart-controls {
        justify-content: center;
    }

    .chart {
        height: 300px;
    }

    .status-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
        text-align: center;
    }

    .insights-grid {
        grid-template-columns: 1fr;
    }
    
    .insights-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .insight-card {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .dashboard {
        padding: 0.5rem;
    }

    .modal-content {
        padding: 1.5rem;
    }

    .input-group {
        flex-direction: column;
    }

    .kpi-card {
        padding: 1rem;
    }

    .chart-container {
        padding: 1rem;
    }
}
